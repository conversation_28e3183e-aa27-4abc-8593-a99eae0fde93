﻿namespace UBEE.API.SERVICES.Controllers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.Shared.DataTransferObjects.Auth;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using static UBEE.Shared.Enums.AppSettings;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;


/// <summary>
/// Authentification CLASS.
/// </summary>
[Route("api/auth")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly UserManager<IdentityUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IConfiguration _configuration;
        private readonly UBEEContext _context;
        //private readonly ConsumersService _consumersService;
        //private readonly TokenService _tokenService;
        //private readonly EmployeeService _employeeService;
        //private readonly EmployeeAssignmentsService _employeeAssignmentsService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userManager"></param>
        /// <param name="roleManager"></param>
        /// <param name="configuration"></param>
        /// <param name="context"></param>
        /// <param name="consumersService"></param>
        /// <param name="tokenService"></param>
        /// <param name="employeeService"></param>
        /// <param name="employeeAssignmentsService"></param>
        public AuthController(UserManager<IdentityUser> userManager, RoleManager<IdentityRole> roleManager, IConfiguration configuration, UBEEContext context/*, ConsumersService consumersService, TokenService tokenService, EmployeeService employeeService, EmployeeAssignmentsService employeeAssignmentsService*/)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _configuration = configuration;
            _context = context;
            //_consumersService = consumersService;
            //_tokenService = tokenService;
            //_employeeService = employeeService;
            //_employeeAssignmentsService = employeeAssignmentsService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("login")]
        public async Task<IActionResult> Login([FromBody] LoginDto model)
        {
        var user = await _userManager.FindByNameAsync(model.Username);
        //var user = await _context.FirstOrDefaultAsync(u => u.UserName == model.Username || u.RfidCode == model.Username);

            if (user != null && await _userManager.CheckPasswordAsync(user, model.Password))
            {
                var userRoles = await _userManager.GetRolesAsync(user);

                var authClaims = new List<Claim>
                {
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new Claim("id", user.Id.ToString()),
                    new Claim("email", user.Email),
                    new Claim(ClaimTypes.Name, user.UserName),
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                };

                foreach (var userRole in userRoles)
                {
                    authClaims.Add(new Claim("role", userRole));
                }

                var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JWT:Secret"]));
                var expirationDate = DateTime.Now.AddMonths(1);
                var token = new JwtSecurityToken(
                    issuer: _configuration["JWT:ValidIssuer"],
                    audience: _configuration["JWT:ValidAudience"],
                    expires: expirationDate,
                    claims: authClaims,
                    signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
                );

                return Ok(new
                {
                    accessToken = new JwtSecurityTokenHandler().WriteToken(token),
                    tokenType = "Bearer",
                    expiration = expirationDate,
                    user = new
                    {
                        username = user.UserName,
                        email = user.Email,
                        roles = userRoles,
                        id = user.Id
                    }
                });
            }
            return Unauthorized();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("register")]
        public async Task<IActionResult> Register([FromBody] RegisterDto model)
        {
            var userExists = await _userManager.FindByNameAsync(model.Email);
            if (userExists != null)
                return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User already exists!" });
            
            try
            {
                var appUserId = Guid.NewGuid().ToString();
                IdentityUser applicationUser = new()
                {
                    Id = appUserId,
                    Email = model.Email,
                    UserName = model.Email,
                    PhoneNumber = model.PhoneNumber,
                    SecurityStamp = Guid.NewGuid().ToString()
                };


                var result = await _userManager.CreateAsync(applicationUser, model.Password);
                if (!result.Succeeded)
                    return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User creation failed! Please check user details and try again." });

                if (!await _roleManager.RoleExistsAsync(model.UserRole.ToUpper()))
                    await _roleManager.CreateAsync(new IdentityRole { Name = model.UserRole.ToUpper(), NormalizedName = model.UserRole.ToUpper() });

                if (await _roleManager.RoleExistsAsync(model.UserRole.ToUpper()))
                {
                    await _userManager.AddToRoleAsync(applicationUser, model.UserRole.ToUpper());
                }

                //UserDto user = new()
                //{
                //    FirstName = model.FirstName,
                //    LastName = model.LastName,
                //    Email = model.Email,
                //    UserName = model.Email,
                //    BirthDate = model.BirthDate,
                //    WorkId = model.WorkId,
                //    Password = model.WorkId,
                //    IdentityCard = model.IdentityCard,
                //    PhoneNumber = model.PhoneNumber,
                //};

                //_context.Users.Add(user);
                //var res = await _context.SaveChangesAsync();
                //applicationUser.UserId = user.Id == Guid.Empty ? null : user.Id;
                //_context.Entry(applicationUser).State = Microsoft.EntityFrameworkCore.EntityState.Modified;

                //res = await _context.SaveChangesAsync();


                return Ok(new { Status = "Success", Message = "User created successfully!" });
              
                
            }
            catch
            {
                return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User creation failed! Please check user details and try again." });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //[HttpPost]
        //[Route("register-admin")]
        //[Authorize]
        //public async Task<IActionResult> RegisterAdmin([FromBody] RegisterDto model)
        //{
        //    var userExists = await _userManager.FindByNameAsync(model.Email);
        //    if (userExists != null)
        //        return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User already exists!" });
        //    using var transaction = _context.Database.BeginTransaction();
        //    try
        //    {

        //        if (model.Password != model.ConfirmPassword)
        //            return StatusCode(StatusCodes.Status400BadRequest, new { Message = "Confirm password doesn't match the password" });

        //        IdentityUser applicationUser = new()
        //        {
        //            Email = model.Email,
        //            UserName = model.Email,
        //            PhoneNumber = model.PhoneNumber,
        //            SecurityStamp = Guid.NewGuid().ToString()
        //        };
        //        var result = await _userManager.CreateAsync(applicationUser, model.Password);

        //        if (!result.Succeeded)
        //            return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User creation failed! Please check user details and try again." });

        //        if (!await _roleManager.RoleExistsAsync(UserRoles.Admin))
        //            await _roleManager.CreateAsync(new IdentityRole { Name = UserRoles.Admin, NormalizedName = UserRoles.Admin.ToUpper() });
        //        if (!await _roleManager.RoleExistsAsync(UserRoles.User))
        //            await _roleManager.CreateAsync(new IdentityRole { Name = UserRoles.User, NormalizedName = UserRoles.User.ToUpper() });
        //        if (!await _roleManager.RoleExistsAsync(UserRoles.Inspector))
        //            await _roleManager.CreateAsync(new IdentityRole { Name = UserRoles.Inspector, NormalizedName = UserRoles.Inspector.ToUpper() });

        //        if (await _roleManager.RoleExistsAsync(UserRoles.Admin))
        //        {
        //            await _userManager.AddToRoleAsync(applicationUser, UserRoles.Admin);
        //        }
        //        transaction.Commit();
        //        return Ok(new { Status = "Success", Message = "User created successfully!" });
        //    }
        //    catch
        //    {
        //        await transaction.RollbackAsync();
        //        return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User creation failed! Please check user details and try again." });
        //    }
        //}

        /// <summary>
        /// For mobile.
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //[HttpPost("LoginWithRfid")]
        //public async Task<IActionResult> LoginWithRfid([FromBody] RfidLoginModel model)
        //{
        //    var assignment = await _employeeAssignmentsService.Assign(model, DateTime.Now);

        //    if (!assignment.Any(s => s.RfidCode == model.RfidCode)) return BadRequest("Did not assign, assignment was null.");

        //    var currentAssignent = assignment.Where(s => s.RfidCode == model.RfidCode).FirstOrDefault();

        //    if (currentAssignent is not null)
        //    {
        //        var user = await _context.ApplicationUsers.FirstOrDefaultAsync(u => u.RfidCode == model.RfidCode);
        //        var userRoles = await _userManager.GetRolesAsync(user);

        //        var authClaims = new List<Claim>
        //        {
        //            new Claim("username", currentAssignent.UserName),
        //            new Claim(ClaimTypes.NameIdentifier, currentAssignent.AspUserId.ToString()),
        //            new Claim("id", currentAssignent.AspUserId.ToString()),
        //            new Claim("email", currentAssignent.Email),
        //            new Claim("rfid", currentAssignent.RfidCode),
        //            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
        //        };

        //        foreach (var userRole in userRoles)
        //        {
        //            authClaims.Add(new Claim("role", userRole));
        //        }

        //        var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JWT:Secret"]));
        //        var expirationDate = DateTime.Now.AddMonths(1);
        //        var token = new JwtSecurityToken(
        //            issuer: _configuration["JWT:ValidIssuer"],
        //            audience: _configuration["JWT:ValidAudience"],
        //            expires: expirationDate,
        //            claims: authClaims,
        //            signingCredentials: new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha256)
        //        );

        //        var consumers = await _consumersService.GetConsumers(currentAssignent.LineCode);
        //        var controllers = await _employeeService.GetUsersControllers();

        //        return Ok(new
        //        {
        //            busCode = currentAssignent.VehicleCode,
        //            lineCode = currentAssignent.LineCode,
        //            currentUser = new
        //            {
        //                accessToken = new JwtSecurityTokenHandler().WriteToken(token),
        //                tokenType = "Bearer",
        //                expiration = expirationDate,
        //                user = new
        //                {
        //                    username = currentAssignent.UserName,
        //                    email = currentAssignent.Email,
        //                    roles = userRoles,
        //                    id = currentAssignent.AspUserId,
        //                    rfid = currentAssignent.RfidCode,
        //                    qrCode = currentAssignent.WorkId,
        //                    name = currentAssignent.DisplayName,
        //                },
        //                employeeType = currentAssignent.Role,
        //            },
        //            consumers,
        //            controllers,
        //        });
        //    }

        //    return Unauthorized("currentAssignment is null");

        //}

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ForgotPassword")]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordDto model)
        {
            //var user = await _context.Users
            //    .Include(s => s.ApplicationUser)
            //    .FirstOrDefaultAsync(u => u.Email == model.Email && u.IdentityCard == model.IdentityCard);

            //if (user != null)
            //{
            //    var token = await _userManager.GeneratePasswordResetTokenAsync(user.ApplicationUser);
            //    var result = await _userManager.ResetPasswordAsync(user.ApplicationUser, token, model.Password);
            //    if (!result.Succeeded)
            //        return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "Unable To Change The Password." });
            //    return StatusCode(StatusCodes.Status200OK, new { Status = "Success", Message = "Password was changed Successfully." });
            //}
            return Unauthorized();
        }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    //[HttpPost]
    //[Route("RegisterEmployee")]
    //[Authorize]
    //public async Task<ActionResult<ApplicationUser>> RegisterEmployee([FromBody] RegisterEmployee model)
    //{
    //    if (model is null)
    //    {
    //        throw new ArgumentNullException("Model is null");
    //    }

    //    var modelInDb = await _context.ApplicationUsers.Include(d => d.User).FirstOrDefaultAsync(s => s.Id == model.Id);

    //    if (modelInDb is null)
    //    {
    //        var username = model.FirstName.Substring(0, 1).ToLower().Replace(" ", "") + "." + model.LastName.ToLower().Replace(" ", "") + "@sdmb.ma";

    //        var employeeTypeId = await _context.EmployeeTypes.Where(s => s.Name.ToLower() == model.Role.ToLower()).Select(s => s.Id).FirstOrDefaultAsync();

    //        if (employeeTypeId == Guid.Empty)
    //        {
    //            throw new NullReferenceException($"employeeTypeId is null for role {model.Role}");
    //        }

    //        ApplicationUser applicationUser = new()
    //        {
    //            Email = username,
    //            UserName = username,
    //            PhoneNumber = model.PhoneNumber,
    //            SecurityStamp = Guid.NewGuid().ToString(),
    //            RfidCode = model.RfidCode,
    //            User = new User
    //            {
    //                FirstName = model.FirstName,
    //                LastName = model.LastName,
    //                BirthDate = model.DOB,
    //                CreatedAt = DateTime.Now,
    //                Email = username,
    //                Password = model.Password,
    //                PhoneNumber = model.PhoneNumber,
    //                IdentityCard = model.IdentityCard,
    //                UserName = username,
    //                WorkId = model.WorkId,
    //                EmployeeTypeId = employeeTypeId
    //            }
    //        };

    //        var result = await _userManager.CreateAsync(applicationUser, model.Password);

    //        if (!result.Succeeded)
    //            return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User creation failed! Please check user details and try again." });

    //        await _userManager.AddToRoleAsync(applicationUser, model.Role);

    //        applicationUser.UserId = applicationUser.User.Id;
    //        _context.Entry(applicationUser).State = EntityState.Modified;

    //        var employee = new Employee
    //        {
    //            FirstName = model.FirstName,
    //            LastName = model.LastName,
    //            BirthDate = model.DOB,
    //            CreatedAt = DateTime.Now,
    //            EmployeeTypeId = employeeTypeId,
    //            IdentityCard = model.IdentityCard,
    //            WorkId = model.WorkId
    //        };

    //        await _context.Employees.AddAsync(employee);
    //        await _context.SaveChangesAsync();

    //        return StatusCode(StatusCodes.Status200OK, new { Status = "Success", Message = "User created successfully." });
    //    }
    //    else
    //    {
    //        var olduser = await _userManager.FindByIdAsync(model.Id);
    //        var role = await _context.AspNetRoles.Where(s => s.Name.ToLower() == model.OldRole.ToLower()).FirstOrDefaultAsync();
    //        var oldrole = await _roleManager.FindByIdAsync(role.Id);

    //        await _userManager.RemoveFromRoleAsync(modelInDb, oldrole.Name);
    //        var resultRole = await _userManager.AddToRoleAsync(modelInDb, model.Role);

    //        var employeeTypeId = await _context.EmployeeTypes.Where(s => s.Name.ToLower() == model.Role.ToLower()).Select(s => s.Id).FirstOrDefaultAsync();
    //        var username = model.FirstName.ToLower().Replace(" ", "") + "." + model.LastName.ToLower().Replace(" ", "") + "@harakiatberkane.ma";

    //        modelInDb.UserName = username;
    //        modelInDb.Email = username;
    //        modelInDb.PhoneNumber = model.PhoneNumber;
    //        modelInDb.RfidCode = model.RfidCode;
    //        modelInDb.User.FirstName = model.FirstName;
    //        modelInDb.User.LastName = model.LastName;
    //        modelInDb.User.BirthDate = model.DOB;
    //        modelInDb.User.Password = model.Password;
    //        modelInDb.User.PhoneNumber = model.PhoneNumber;
    //        modelInDb.User.IdentityCard = model.IdentityCard;
    //        modelInDb.User.WorkId = model.WorkId;
    //        modelInDb.User.EmployeeTypeId = employeeTypeId;
    //        modelInDb.User.UserName = username;
    //        modelInDb.User.Email = username;
    //        modelInDb.User.Password = model.Password;

    //        var employee = await _context.Employees.Where(s => s.WorkId == model.OldWorkId).FirstOrDefaultAsync();
    //        employee.FirstName = model.FirstName;
    //        employee.LastName = model.LastName;
    //        employee.BirthDate = model.DOB;
    //        employee.EmployeeTypeId = employeeTypeId;
    //        employee.IdentityCard = model.IdentityCard;
    //        employee.WorkId = model.WorkId;

    //        var token = await _userManager.GeneratePasswordResetTokenAsync(modelInDb);
    //        var result = await _userManager.ResetPasswordAsync(modelInDb, token, model.Password);

    //        if (result.Succeeded)
    //        {
    //            _context.Entry(modelInDb).State = EntityState.Modified;
    //            _context.Entry(employee).State = EntityState.Modified;
    //            await _context.SaveChangesAsync();
    //            return StatusCode(StatusCodes.Status200OK, new { Status = "Success", Message = "User updated successfully." });
    //        }

    //        return StatusCode(StatusCodes.Status400BadRequest, new { Status = "Error", Message = result.Errors });
    //    }

    //    return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "Please verify your model something went wrong." });
    //}
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    //[HttpGet("Users")]
    //public async Task<ActionResult<List<RegisterEmployee>>> GetListOfUsers()
    //{
    //    var rolesPermitted = new string[]
    //   {
    //    EmployeeTypes.CONTROLLER.ToLower(),
    //    EmployeeTypes.CASHIER.ToLower(),
    //    EmployeeTypes.DRIVER.ToLower(),
    //   };

    //    return await _context.ApplicationUsers.Where(s => rolesPermitted.Contains(s.User.EmployeeType.Name.ToLower())).Select(s => new RegisterEmployee
    //    {
    //        FirstName = s.User.FirstName,
    //        LastName = s.User.LastName,
    //        Email = s.Email,
    //        WorkId = s.User.WorkId,
    //        Role = s.User.EmployeeType.Name,
    //        RfidCode = s.RfidCode,
    //        Id = s.Id
    //    }).OrderBy(s => s.FirstName).ToListAsync();
    //}
    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    //[HttpGet("Users/{id}")]
    //public async Task<ActionResult<RegisterEmployee>> GetUser(string id)
    //{
    //    return await _context.ApplicationUsers.Where(s => s.Id == id).Select(s => new RegisterEmployee
    //    {
    //        FirstName = s.User.FirstName,
    //        LastName = s.User.LastName,
    //        WorkId = s.User.WorkId,
    //        Role = s.User.EmployeeType.Name,
    //        RfidCode = s.RfidCode,
    //        Id = s.Id,
    //        DOB = s.User.BirthDate,
    //        PhoneNumber = s.PhoneNumber,
    //        IdentityCard = s.User.IdentityCard,
    //        OldRole = s.User.EmployeeType.Name,
    //        OldWorkId = s.User.WorkId,
    //        Password = s.User.Password,
    //        ConfirmPassword = s.User.Password,
    //    }).FirstOrDefaultAsync();
    //}

    //[HttpGet("CreateUsers")]
    //public async Task<IActionResult> CreateUsers()
    //{
    //    var list = new List<RegisterModelEmp>();

    //    var users = await _context.Users.Include(s => s.EmployeeType).ToListAsync();

    //    foreach (var user in users)
    //    {

    //        if (user.EmployeeTypeId.HasValue && user.ApplicationUserId == null && user.EmployeeTypeId == Guid.Parse("0152D200-FE06-418D-9EFA-72E13EFB32E2") && user.Id != Guid.Parse("07C3453E-F36B-1410-8373-005A40706DBC"))
    //        {
    //            var register = new RegisterModelEmp
    //            {
    //                WorkId = user.WorkId,
    //                Email = user.Email,
    //                PhoneNumber = user.PhoneNumber,
    //                Password = user.Password + "000112",
    //                ConfirmPassword = user.Password + "000112",
    //                EmployeeType = user.EmployeeType.Name,
    //                UserId = user.Id
    //            };

    //            var result = await RegisterEmployeesToUsers(register);
    //            if (result != null)
    //            {
    //                user.ApplicationUserId = result.Id;
    //                _context.Entry(user).State = EntityState.Modified;
    //                await _context.SaveChangesAsync();
    //                list.Add(register);
    //            }
    //        }
    //    }
    //    return Ok(list);
    //}

    //[HttpPost]
    //[Route("RegisterEmployeesToUsers")]
    //[Authorize]
    //public async Task<ApplicationUser> RegisterEmployeesToUsers([FromBody] RegisterModelEmp model)
    //{
    //    var userExists = await _userManager.FindByNameAsync(model.Email);
    //    if (userExists != null)
    //        //return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User already exists!" });
    //        return null;
    //    using var transaction = _context.Database.BeginTransaction();
    //    try
    //    {

    //        if (model.Password != model.ConfirmPassword)
    //            //return StatusCode(StatusCodes.Status400BadRequest, new { Message = "Confirm password doesn't match the password" });
    //            return null;

    //        ApplicationUser applicationUser = new()
    //        {
    //            Email = String.Concat(model.Email.Where(c => !Char.IsWhiteSpace(c))),
    //            UserName = String.Concat(model.Email.Where(c => !Char.IsWhiteSpace(c))),
    //            PhoneNumber = model.PhoneNumber,
    //            SecurityStamp = Guid.NewGuid().ToString(),
    //            RfidCode = model.RfidCode,
    //            UserId = model.UserId
    //        };

    //        var result = await _userManager.CreateAsync(applicationUser, model.Password);

    //        if (!result.Succeeded)
    //            //return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User creation failed! Please check user details and try again." });
    //            return null;

    //        if (!await _roleManager.RoleExistsAsync(UserRoles.Admin))
    //            await _roleManager.CreateAsync(new IdentityRole { Name = UserRoles.Admin, NormalizedName = UserRoles.Admin.ToUpper() });
    //        if (!await _roleManager.RoleExistsAsync(UserRoles.User))
    //            await _roleManager.CreateAsync(new IdentityRole { Name = UserRoles.User, NormalizedName = UserRoles.User.ToUpper() });
    //        if (!await _roleManager.RoleExistsAsync(UserRoles.Inspector))
    //            await _roleManager.CreateAsync(new IdentityRole { Name = UserRoles.Inspector, NormalizedName = UserRoles.Inspector.ToUpper() });
    //        if (!await _roleManager.RoleExistsAsync(model.EmployeeType))
    //            await _roleManager.CreateAsync(new IdentityRole { Name = model.EmployeeType, NormalizedName = model.EmployeeType.ToUpper() });

    //        if (await _roleManager.RoleExistsAsync(model.EmployeeType))
    //        {
    //            await _userManager.AddToRoleAsync(applicationUser, model.EmployeeType);
    //        }

    //        transaction.Commit();
    //        await _context.SaveChangesAsync();
    //        return applicationUser;
    //    }
    //    catch (Exception ex)
    //    {
    //        await transaction.RollbackAsync();
    //        //return StatusCode(StatusCodes.Status500InternalServerError, new { Status = "Error", Message = "User creation failed! Please check user details and try again." });
    //        return null;
    //    }
    //}



    /// <summary>
    /// Get all users with their basic information and roles
    /// </summary>
    /// <returns>List of users</returns>
    [HttpGet]
    [Route("users")]
    public async Task<IActionResult> GetAllUsers()
    {
        try
        {
            var users = await _userManager.Users.ToListAsync();
            var userList = new List<object>();

            foreach (var user in users)
            {
                var roles = await _userManager.GetRolesAsync(user);
                userList.Add(new
                {
                    id = user.Id,
                    userName = user.UserName,
                    email = user.Email,
                    phoneNumber = user.PhoneNumber,
                    roles = roles,
                    emailConfirmed = user.EmailConfirmed,
                    lockoutEnabled = user.LockoutEnabled,
                    lockoutEnd = user.LockoutEnd,
                    accessFailedCount = user.AccessFailedCount
                });
            }

            return Ok(new { Status = "Success", Data = userList });
        }
        catch (Exception ex)
        {
            return StatusCode(StatusCodes.Status500InternalServerError,
                new { Status = "Error", Message = "Failed to retrieve users." });
        }
    }

    /// <summary>
    /// Get a specific user by ID
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>User details</returns>
    [HttpGet]
    [Route("users/{id}")]
    public async Task<IActionResult> GetUserById(string id)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return NotFound(new { Status = "Error", Message = "User not found." });

            var roles = await _userManager.GetRolesAsync(user);

            return Ok(new
            {
                Status = "Success",
                Data = new
                {
                    id = user.Id,
                    userName = user.UserName,
                    email = user.Email,
                    phoneNumber = user.PhoneNumber,
                    roles = roles,
                    emailConfirmed = user.EmailConfirmed,
                    lockoutEnabled = user.LockoutEnabled,
                    lockoutEnd = user.LockoutEnd,
                    accessFailedCount = user.AccessFailedCount
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(StatusCodes.Status500InternalServerError,
                new { Status = "Error", Message = "Failed to retrieve user." });
        }
    }

    /// <summary>
    /// Update user information
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="model">Update user model</param>
    /// <returns>Updated user result</returns>
    [HttpPut]
    [Route("users/{id}")]
    public async Task<IActionResult> UpdateUser(string id, [FromBody] UpdateUserDto model)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return NotFound(new { Status = "Error", Message = "User not found." });

            // Update basic user information
            if (!string.IsNullOrEmpty(model.Email))
            {
                var existingUser = await _userManager.FindByEmailAsync(model.Email);
                if (existingUser != null && existingUser.Id != id)
                    return BadRequest(new { Status = "Error", Message = "Email already exists." });

                user.Email = model.Email;
            }

            if (!string.IsNullOrEmpty(model.UserName))
            {
                var existingUser = await _userManager.FindByNameAsync(model.UserName);
                if (existingUser != null && existingUser.Id != id)
                    return BadRequest(new { Status = "Error", Message = "Username already exists." });

                user.UserName = model.UserName;
            }

            if (!string.IsNullOrEmpty(model.PhoneNumber))
                user.PhoneNumber = model.PhoneNumber;

            if (model.EmailConfirmed.HasValue)
                user.EmailConfirmed = model.EmailConfirmed.Value;

            if (model.LockoutEnabled.HasValue)
                user.LockoutEnabled = model.LockoutEnabled.Value;

            var updateResult = await _userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { Status = "Error", Message = "Failed to update user information.", Errors = updateResult.Errors });

            // Update password if provided
            if (!string.IsNullOrEmpty(model.NewPassword))
            {
                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                var passwordResult = await _userManager.ResetPasswordAsync(user, token, model.NewPassword);
                if (!passwordResult.Succeeded)
                    return StatusCode(StatusCodes.Status500InternalServerError,
                        new { Status = "Error", Message = "Failed to update password.", Errors = passwordResult.Errors });
            }

            // Update roles if provided
            if (model.Roles != null && model.Roles.Any())
            {
                var currentRoles = await _userManager.GetRolesAsync(user);
                var removeResult = await _userManager.RemoveFromRolesAsync(user, currentRoles);

                if (removeResult.Succeeded)
                {
                    // Ensure roles exist
                    foreach (var role in model.Roles)
                    {
                        if (!await _roleManager.RoleExistsAsync(role.ToUpper()))
                            await _roleManager.CreateAsync(new IdentityRole { Name = role.ToUpper(), NormalizedName = role.ToUpper() });
                    }

                    var addResult = await _userManager.AddToRolesAsync(user, model.Roles.Select(r => r.ToUpper()));
                    if (!addResult.Succeeded)
                        return StatusCode(StatusCodes.Status500InternalServerError,
                            new { Status = "Error", Message = "Failed to update user roles.", Errors = addResult.Errors });
                }
            }

            // Return updated user info
            var updatedRoles = await _userManager.GetRolesAsync(user);
            return Ok(new
            {
                Status = "Success",
                Message = "User updated successfully.",
                Data = new
                {
                    id = user.Id,
                    userName = user.UserName,
                    email = user.Email,
                    phoneNumber = user.PhoneNumber,
                    roles = updatedRoles,
                    emailConfirmed = user.EmailConfirmed,
                    lockoutEnabled = user.LockoutEnabled
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(StatusCodes.Status500InternalServerError,
                new { Status = "Error", Message = "Failed to update user.", Error = ex.Message });
        }
    }

    public class UpdateUserDto
    {
        [EmailAddress]
        public string? Email { get; set; }

        public string? UserName { get; set; }

        [Phone]
        public string? PhoneNumber { get; set; }

        [MinLength(6)]
        public string? NewPassword { get; set; }

        public List<string>? Roles { get; set; }

        public bool? EmailConfirmed { get; set; }

        public bool? LockoutEnabled { get; set; }
    }
    /// <summary>
    /// Delete a user by ID
    /// </summary>
    /// <param name="id">User ID to delete</param>
    /// <returns>Deletion result</returns>
    [HttpDelete]
    [Route("users/{id}")]
    public async Task<IActionResult> DeleteUser(string id)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return NotFound(new { Status = "Error", Message = "User not found." });

            // Prevent deletion of current user (optional)
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (user.Id == currentUserId)
                return BadRequest(new { Status = "Error", Message = "You cannot delete your own account." });

            // Delete user
            var result = await _userManager.DeleteAsync(user);
            if (!result.Succeeded)
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { Status = "Error", Message = "Failed to delete user.", Errors = result.Errors });

            return Ok(new { Status = "Success", Message = "User deleted successfully." });
        }
        catch (Exception ex)
        {
            return StatusCode(StatusCodes.Status500InternalServerError,
                new { Status = "Error", Message = "An error occurred while deleting the user." });
        }
    }
}

