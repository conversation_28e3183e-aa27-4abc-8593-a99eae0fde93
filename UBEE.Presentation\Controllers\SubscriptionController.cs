﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/subscription")]
[ApiController]
public class SubscriptionController : BaseController<Subscription>
{
    private readonly ISubscriptionService _service;
    public SubscriptionController(ISubscriptionService baseService) : base(baseService)
    {
        this._service = baseService as ISubscriptionService;
        this.UnwantedProperties += "";
        IncludesProperties = "Licence.ControllerServeurs,SubscribedOptions";
    }
}