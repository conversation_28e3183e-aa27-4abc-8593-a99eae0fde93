﻿using UBEE.Contracts.Repository;
using UBEE.Contracts.Repository.Base;
using UBEE.MODELS.Common.Models;
using UBEE.MODELS.DataSource;
using UBEE.Repository.Repository.Base;

namespace UBEE.Repository.Repository;

public class RepositoryManager : IRepositoryManager
{
    #region private properties

    /// <summary>
    /// The context
    /// </summary>
    private readonly UBEEContext _context;

    private readonly Dictionary<string, IGenericRepository> _repositories;

    /// <summary>
    /// The disposed
    /// </summary>
    private bool _disposed = false;

    #endregion

    #region contructors

    /// <summary>
    /// Initializes a new instance of the <see cref="UnitOfWork"/> class.
    /// </summary>
    public RepositoryManager(UBEEContext context)
    {
        _repositories = new Dictionary<string, IGenericRepository>();
        //_context = new LicenceServerContext();
        _context = context;
    }
    #endregion
    #region public properties
    /// <summary>
    /// 
    /// </summary>
    public UBEEContext Context => _context;

    #endregion
    #region protected methods

    /// <summary>
    /// Releases unmanaged and - optionally - managed resources.
    /// </summary>
    /// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _context.Dispose();
        }

        _disposed = true;
    }

    #endregion

    #region public methods

    /// <summary>
    /// Get Repository by Entity
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    /// <returns></returns>
    public IGenericRepository<TEntity> GetRepository<TEntity>() where TEntity : BaseClass
    {
        var repositoryName = typeof(TEntity).Name + "Repository";
        var typeName = "UBEE.Repository." + repositoryName;

        Type repositoryType = Type.GetType(typeName);

        if (_repositories.ContainsKey(repositoryName))
            return (IGenericRepository<TEntity>)_repositories[repositoryName];
        if (repositoryType == null)
        {
            _repositories[repositoryName] = (IGenericRepository<TEntity>)Activator.CreateInstance(
                typeof(GenericRepository<TEntity>),
                _context);
        }
        else
        {
            _repositories[repositoryName] = (IGenericRepository<TEntity>)Activator.CreateInstance(
                repositoryType,
             _context);
        }



        return (IGenericRepository<TEntity>)_repositories[repositoryName];
    }


    /// <summary>
    /// The Save Method
    /// </summary>
    public int Save()
    {
        return _context.SaveChanges();
    }

    public async Task<int> SaveAsync()
    {
        return await _context.SaveChangesAsync();
    }

    /// <inheritdoc />
    /// <summary>
    /// Exécute les tâches définies par l'application associées à la libération ou à la redéfinition des ressources non managées.
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
    #endregion
}

