﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Site : AuditEntity
{
    public string? Name { get; set; }
    public string? Address { get; set; }
    public string? AddressComplement { get; set; }
    public int? EmployeesCount { get; set; }
    public int? LocalsCount { get; set; }
    public string? PhoneNumber { get; set; }
    public byte[]? Image { get; set; }
    public string? Description { get; set; }
    public string? Contact { get; set; }
    public string? Manager { get; set; }
    public string? Email { get; set; }
    public string? Status { get; set; }
    public string? Grade { get; set; }
    public double? Latitude { get; set; }
    public double? Longtitude { get; set; }
    public double? Surface { get; set; }
    public Guid? ClientId { get; set; }
    public Client? Client { get; set; }
    //public List<Local>? Locals { get; set; }
}