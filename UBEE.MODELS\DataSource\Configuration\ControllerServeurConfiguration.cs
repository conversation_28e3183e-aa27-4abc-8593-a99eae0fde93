﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class ControllerServeurConfiguration : IEntityTypeConfiguration<ControllerServeur>
{
    public void Configure(EntityTypeBuilder<ControllerServeur> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasOne(s => s.Licence)
            .WithMany(s => s.ControllerServeurs)
            .HasForeignKey(s => s.IdLicence);

        builder.HasMany(s => s.ControllerServerRules)
            .WithOne(s => s.ControllerServeur)
            .HasForeignKey(s => s.IdControllerServeur);

        builder.HasMany(s => s.ControllerServerControllers)
            .WithOne(s => s.ControllerServeur)
            .HasForeignKey(s => s.IdControllerServeur);
    }
}

