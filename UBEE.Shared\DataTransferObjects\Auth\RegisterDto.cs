﻿using System.ComponentModel.DataAnnotations;

namespace UBEE.Shared.DataTransferObjects.Auth;

public class RegisterDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; }

    [Required]
    [Compare("Password")]
    public string ConfirmPassword { get; set; }
    [Required]
    public string Password { get; set; }

    public string PhoneNumber { get; set; }

    [Required]
    public string UserRole { get; set; }
}
