﻿namespace UBEE.MODELS.DataSource.DTOs
{
    public class RuleRecentApplicationDto
    {
        public Guid RuleId { get; set; }
        public Guid TransactionId { get; set; }
        public DateTime ApplicationTimestamp { get; set; }
        public bool ApplicationSuccess { get; set; } // Placeholder, currently always 1 based on view
        public string LocalName { get; set; }
        public string SiteName { get; set; }
        public long RowNum { get; set; } // ROW_NUMBER() returns a long
    }
}