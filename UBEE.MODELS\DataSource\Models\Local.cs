﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Local : AuditEntity
{
    public string Name { get; set; }
    public int Floor { get; set; }
    public int SensorsCount { get; set; }
    public int Capacity { get; set; }
    public string? Architecture2DImage { get; set; } // Stockée en base de données
    public byte[]? ImageLocal { get; set; } // Image principale du local
    public double Latitude { get; set; }
    public double Longtitude { get; set; }
    public Guid IdSite { get; set; }
    public Site? Site { get; set; }
    public Guid TypeLocalId { get; set; }
    public TypeLocal? TypeLocal { get; set; }
    public List<Transaction>? Transactions { get; set; }
}
