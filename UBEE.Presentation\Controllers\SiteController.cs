﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Presentation.Models;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/site")]
[ApiController]
public class SiteController : BaseController<Site>
{
    private readonly ISiteService _service;
    public SiteController(ISiteService baseService) : base(baseService)
    {
        this._service = baseService as ISiteService;
        this.UnwantedProperties += ",Image";
        WantedProperties = "";
        IncludesProperties = "Client";
    }

    [HttpGet("client/{ClientId}")]
    public virtual async Task<IActionResult> GetByClientId(Guid ClientId)
    {
        UnwantedProperties = "Image,Adress,AddressComplement,EmployeesCount,LocalsCount,PhoneNumber,Description,Contact,Manager,Email,Status,Grade,Latitude,Longtitude,Surface,Client,Locals,CreatedAt,CreatedBy,LastUpdatedAt,LastUpdatedBy,DeletedAt,DeletedBy";
        return Ok((await this.Service.Get(s => s.ClientId == ClientId, includeProperties: "")).MarshelList(WantedProperties, UnwantedProperties));
    }
}

