﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;
using UBEE.Shared.DataTransferObjects.Dto;

namespace UBEE.Presentation.Controllers;

[Route("api/client")]
[ApiController]
public class ClientController : BaseController<Client>
{
    private readonly IClientService _service;
    private readonly UBEEContext _context;

    public ClientController(IClientService baseService, UBEEContext context) : base(baseService)
    {
        this._service = baseService as IClientService;
        this.UnwantedProperties += ",ClientLogo";
        IncludesProperties = "Organisation,Sites,Subscriptions.Licence.ControllerServeurs";
        _context = context;
    }

    [HttpPost("client-activation")]
    public async Task<IActionResult> ActiverClient([FromBody] ActivationDto activationDto)
    {
        var ClientData = await _service.GetOne(activationDto.Id, IncludesProperties);
        if (ClientData.Subscriptions.Count > 0)
        {
            ClientData.Status = "Actif";
            ClientData.ClientStatus = "Actif";
            await _service.Update(ClientData, "Subscriptions");
        }
        return Ok("OK");
    }

    [HttpGet("controllers/{idclient}")]
    public async Task<IActionResult> GetControllersByClientId(Guid idclient)
    {
        var results = await _context.ClientLicenceControllerViews
        .Where(v => v.ClientId == idclient)
        .ToListAsync();
        return Ok(results);
    }
}
