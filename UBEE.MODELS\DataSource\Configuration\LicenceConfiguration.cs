﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class LicenceConfiguration : IEntityTypeConfiguration<Licence>
{
    public void Configure(EntityTypeBuilder<Licence> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasMany(s => s.Factures)
        .WithOne(s => s.Licence)
        .HasForeignKey(s => s.IdLicence);

        builder.HasMany(s => s.ControllerServeurs)
            .WithOne(s => s.Licence)
            .HasForeignKey(s => s.IdLicence);
    }
}

