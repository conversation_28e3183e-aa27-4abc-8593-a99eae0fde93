﻿using System.Dynamic;

using System.Reflection;

using System.Text.RegularExpressions;

using UBEE.MODELS.Common.Models;

namespace UBEE.Presentation.Models;


public static class ExpandoEntity

{

    private const char PropertySeparator = ',';

    private const char NeastedPropertySeparator = '.';

    public static ExpandoObject Marshel(this BaseClass entity, string wantedProperties, string unwantedProperties)

    {

        if (entity == null) return null;

        var output = new ExpandoObject();

        var propertiesToLeave = wantedProperties.Split

            (new[] { PropertySeparator }, StringSplitOptions.RemoveEmptyEntries);

        var propertiesToRemove = unwantedProperties.Split

            (new[] { PropertySeparator }, StringSplitOptions.RemoveEmptyEntries);

        if (entity == null) return null;

        foreach (var propertyInfo in entity.GetType().GetProperties())

        {

            var propertyInfoName = propertyInfo.Name;

            if (wantedProperties.Any() && !propertiesToLeave.Contains(propertyInfoName)

                || unwantedProperties.Any() && propertiesToRemove.Contains(propertyInfoName))

                continue;

            var value = propertyInfo?.GetValue(entity, null);

            ((IDictionary<string, object>)output).Add(propertyInfo.Name, value);

        }

        return output;

    }


    public static ExpandoObject MarshelToDescriptor(this BaseClass entity, string descriptorProperties)

    {

        var output = new ExpandoObject();

        var properties = "Id,";

        var regexItem = new Regex("^[a-zA-Z0-9]*$");

        var separator = ' ';

        if (!regexItem.IsMatch(descriptorProperties))

        {

            separator = GetSeperatorFromUnspecialString(descriptorProperties);

            var propTemp = descriptorProperties.Split

                (new[] { separator }, StringSplitOptions.RemoveEmptyEntries);

            for (var i = 0; i < propTemp.Length; i++)

            {

                properties += i == propTemp.Length - 1 ? propTemp[i] : propTemp[i] + ",";

            }

        }

        else

        {

            properties += descriptorProperties;

        }

        var propertiesToLeave = properties.Split

            (new[] { PropertySeparator }, StringSplitOptions.RemoveEmptyEntries);

        var libelleValue = "";

        foreach (var propertyName in propertiesToLeave)

        {

            //&& !((IDictionary<string, object>)output).ContainsKey("")

            if (propertyName.ToUpper().Equals("ID"))

            {

                ((IDictionary<string, object>)output).Add(propertyName, entity.GetType().GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance)?.GetValue(entity, null));

            }

            else

            {

                libelleValue += !string.IsNullOrEmpty(libelleValue)

                    ? separator.ToString() + entity.GetType()

                          .GetProperty(propertyName,

                              BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance)

                          ?.GetValue(entity, null)

                    : entity.GetType()

                        .GetProperty(propertyName,

                            BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance)

                        ?.GetValue(entity, null).ToString();

            }

        }

        ((IDictionary<string, object>)output).Add("Libelle", libelleValue);

        return output;

    }

    public static IEnumerable<ExpandoObject> MarshelList(this IEnumerable<BaseClass> enumerable, string wantedProperties, string unwantedProperties)

    {

        return enumerable.Select(o => o.Marshel(wantedProperties, unwantedProperties));

    }

    public static IEnumerable<ExpandoObject> MarshelToDescriptorList(this IEnumerable<BaseClass> enumerable, string wantedProperties)

    {

        return enumerable.Select(o => o.MarshelToDescriptor(wantedProperties));

    }

    private static void MarshelNestedProperty(

     ExpandoObject expando,

     object entity,

     string propertyPath)

    {

        if (string.IsNullOrWhiteSpace(propertyPath) || entity == null)

            return;

        // Split e.g. "Address.City" -> ["Address","City"]

        var parts = propertyPath.Split(NeastedPropertySeparator);

        object currentObject = entity;

        var currentExpando = expando;

        for (int i = 0; i < parts.Length; i++)

        {

            var segment = parts[i];

            if (currentObject == null)

                break; // can't go deeper

            // Reflect to find a property that matches the segment

            var propInfo = currentObject.GetType()

                .GetProperty(segment, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

            if (propInfo == null)

                break; // no such property, just stop or throw

            var value = propInfo.GetValue(currentObject);

            // If we're not yet at the final segment, we need to build or reuse an ExpandoObject 

            // so that we can keep nesting deeper.

            if (i < parts.Length - 1)

            {

                // Check if we already have an Expando for this segment.

                if (!((IDictionary<string, object>)currentExpando).ContainsKey(segment))

                {

                    // We create an empty sub-expando and store it.

                    var subExpando = new ExpandoObject();

                    ((IDictionary<string, object>)currentExpando)[segment] = subExpando;

                    currentExpando = subExpando;

                }

                else

                {

                    // Re-use the existing one

                    var existing = ((IDictionary<string, object>)currentExpando)[segment];

                    if (existing is ExpandoObject subExpando)

                    {

                        currentExpando = subExpando;

                    }

                    else

                    {

                        // If there is a collision, you can decide to overwrite or skip.

                        var newSubExpando = new ExpandoObject();

                        ((IDictionary<string, object>)currentExpando)[segment] = newSubExpando;

                        currentExpando = newSubExpando;

                    }

                }

                // Move "down" one level in the actual data object graph

                currentObject = value;

            }

            else

            {

                // Last segment in the path? 

                // Just set the property value in the current ExpandoObject.

                ((IDictionary<string, object>)currentExpando)[segment] = value;

            }

        }

    }

    private static char GetSeperatorFromUnspecialString(string input)

    {

        var separator = ' ';

        foreach (var c in input.ToCharArray())

        {

            if (!char.IsLetterOrDigit(c))

            {

                separator = c;

                break;

            }

        }

        return separator;

    }

}

