﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/capteur")]
[ApiController]
public class CapteurController : BaseController<Capteur>
{
    private readonly ICapteurService _service;
    private readonly UBEEContext _context;
    public CapteurController(ICapteurService baseService, UBEEContext context) : base(baseService)
    {
        this._service = baseService as ICapteurService;
        this.UnwantedProperties += "";
        IncludesProperties = "Controller,TypeCapteur";
        _context = context;
    }

    [HttpGet("site/{idsite}")]
    public async Task<IActionResult> GetCapteursViewBySiteId(Guid idsite)
    {
        var results = await _context.vwsCapteurClientSite
        .Where(v => v.SiteId == idsite)
        .ToListAsync();
        return Ok(results);
    }


    [HttpGet("client/{clientid}")]
    public async Task<IActionResult> GetCapteursViewByClientId(Guid clientid)
    {
        var results = await _context.vwsCapteurClientSite
        .Where(v => v.ClientId == clientid)
        .ToListAsync();

        return Ok(results);

    }


    [HttpGet("local/{localid}")]
    public async Task<IActionResult> GetCapteursViewByLocalId(Guid localid)
    {
        var results = await _context.vwsCapteurClientSite
            .Where(v => v.LocalId == localid)
            .ToListAsync();

        return Ok(results);

    }
}

