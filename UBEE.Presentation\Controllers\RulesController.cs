﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.MODELS.DataSource.DTOs;
using UBEE.Shared.GenericListModel.Filtering;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/rule")]
[ApiController]
public class RulesController : BaseController<Rules>
{
    private readonly IRulesService _service;

    public RulesController(IRulesService baseService) : base(baseService)
    {
        this._service = baseService as IRulesService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }

    /// <summary>
    /// Get all rules with comprehensive statistics and pagination using Lister
    /// </summary>
    //[HttpPost("comprehensive")]
    //public async Task<ActionResult> GetRulesComprehensive([FromBody] Lister<RuleComprehensiveDto> lister)
    //{
    //    try
    //    {
    //        // Set defaults if not provided
    //        lister.Pagination ??= new Pagination { CurrentPage = 1, PageSize = 10 };
    //        lister.SortParams ??= new List<Sorting> { new() { Column = "Priority", Sort = "ASC" } };
    //        lister.FilterParams ??= new List<Filtering.WhereParams>();

    //        var result = await _service.GetRulesComprehensiveAsync(lister);
    //        return Ok(result);
    //    }
    //    catch (Exception ex)
    //    {
    //        return StatusCode(500, $"Error retrieving rules: {ex.Message}");
    //    }
    //}

    /// <summary>
    /// Get all rules with comprehensive statistics and pagination using query parameters
    /// </summary>
    [HttpGet("comprehensive")]
    public async Task<ActionResult> GetRulesComprehensive(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? sortBy = "Priority",
        [FromQuery] string? sortDirection = "ASC")
    {
        try
        {
            var lister = new Lister<RuleComprehensiveDto>
            {
                Pagination = new Pagination
                {
                    CurrentPage = Math.Max(1, page),
                    PageSize = Math.Max(1, Math.Min(100, pageSize))
                },
                SortParams = new List<Sorting>
                {
                    new() { Column = sortBy ?? "Priority", Sort = sortDirection ?? "ASC" }
                },
                FilterParams = new List<Filtering.WhereParams>()
            };

            var result = await _service.GetRulesComprehensiveAsync(lister);

            // Add pagination headers for easier frontend consumption
            Response.Headers.Add("X-Pagination-CurrentPage", result.Lister.Pagination.CurrentPage.ToString());
            Response.Headers.Add("X-Pagination-PageSize", result.Lister.Pagination.PageSize.ToString());
            Response.Headers.Add("X-Pagination-TotalItems", result.Lister.Pagination.TotalElement.ToString());
            Response.Headers.Add("X-Pagination-TotalPages", result.Lister.Pagination.PageCount.ToString());
            Response.Headers.Add("X-Pagination-IsFirst", result.Lister.Pagination.IsFirst.ToString());
            Response.Headers.Add("X-Pagination-IsLast", result.Lister.Pagination.IsLast.ToString());

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rules: {ex.Message}");
        }
    }

    /// <summary>
    /// Get all rules with comprehensive statistics (no pagination for backward compatibility)
    /// </summary>
    [HttpGet("comprehensive/all")]
    public async Task<ActionResult> GetAllRulesComprehensive()
    {
        try
        {
            var rules = await _service.GetRulesComprehensiveAsync();
            return Ok(rules);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rules: {ex.Message}");
        }
    }

    /// <summary>
    /// Search rules with pagination using RulesLister
    ///// </summary>
    //[HttpPost("search")]
    //public async Task<ActionResult> SearchRules([FromBody] RulesLister rulesLister)
    //{
    //    try
    //    {
    //        if (string.IsNullOrWhiteSpace(rulesLister.SearchTerm))
    //            return BadRequest("Search term is required");

    //        // Set defaults if not provided
    //        rulesLister.Pagination ??= new Pagination { CurrentPage = 1, PageSize = 10 };
    //        rulesLister.SortParams ??= new List<Sorting> { new() { Column = "Priority", Sort = "ASC" } };
    //        rulesLister.FilterParams ??= new List<Filtering.WhereParams>();

    //        var result = await _service.SearchRulesAsync(rulesLister);
    //        return Ok(result);
    //    }
    //    catch (Exception ex)
    //    {
    //        return StatusCode(500, $"Error searching rules: {ex.Message}");
    //    }
    //}

    /// <summary>
    /// Search rules by name or tags with pagination using query parameters
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult> SearchRules(
        [FromQuery] string searchTerm,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? sortBy = "Priority",
        [FromQuery] string? sortDirection = "ASC",
        [FromQuery] bool includeInactive = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return BadRequest("Search term is required");

            var rulesLister = new RulesLister
            {
                SearchTerm = searchTerm,
                IncludeInactive = includeInactive,
                Pagination = new Pagination
                {
                    CurrentPage = Math.Max(1, page),
                    PageSize = Math.Max(1, Math.Min(100, pageSize))
                },
                SortParams = new List<Sorting>
                {
                    new() { Column = sortBy ?? "Priority", Sort = sortDirection ?? "ASC" }
                },
                FilterParams = new List<Filtering.WhereParams>()
            };

            var result = await _service.SearchRulesAsync(rulesLister);

            // Add pagination headers
            Response.Headers.Add("X-Pagination-CurrentPage", result.Lister.Pagination.CurrentPage.ToString());
            Response.Headers.Add("X-Pagination-PageSize", result.Lister.Pagination.PageSize.ToString());
            Response.Headers.Add("X-Pagination-TotalItems", result.Lister.Pagination.TotalElement.ToString());
            Response.Headers.Add("X-Pagination-TotalPages", result.Lister.Pagination.PageCount.ToString());
            Response.Headers.Add("X-Pagination-IsFirst", result.Lister.Pagination.IsFirst.ToString());
            Response.Headers.Add("X-Pagination-IsLast", result.Lister.Pagination.IsLast.ToString());

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error searching rules: {ex.Message}");
        }
    }

    /// <summary>
    /// Search rules by name or tags (no pagination for backward compatibility)
    /// </summary>
    [HttpGet("search/all")]
    public async Task<ActionResult> SearchAllRules([FromQuery] string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return BadRequest("Search term is required");

            var rules = await _service.SearchRulesAsync(searchTerm);
            return Ok(rules);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error searching rules: {ex.Message}");
        }
    }

    /// <summary>
    /// Get a specific rule with comprehensive details
    /// </summary>
    [HttpGet("{ruleId:guid}/comprehensive")]
    public async Task<ActionResult> GetRuleComprehensive(Guid ruleId)
    {
        try
        {
            var rule = await _service.GetRuleComprehensiveByIdAsync(ruleId);
            if (rule == null)
                return NotFound($"Rule with ID {ruleId} not found");

            return Ok(rule);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rule: {ex.Message}");
        }
    }

    /// <summary>
    /// Get the client hierarchy for a specific rule (for expanded details)
    /// </summary>
    [HttpGet("{ruleId:guid}/hierarchy")]
    public async Task<ActionResult> GetRuleClientHierarchy(Guid ruleId)
    {
        try
        {
            var hierarchy = await _service.GetRuleClientHierarchyAsync(ruleId);
            return Ok(hierarchy);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rule hierarchy: {ex.Message}");
        }
    }

    /// <summary>
    /// Get tags for a specific rule
    /// </summary>
    [HttpGet("{ruleId:guid}/tags")]
    public async Task<ActionResult> GetRuleTags(Guid ruleId)
    {
        try
        {
            var tags = await _service.GetRuleTagsAsync(ruleId);
            return Ok(tags);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rule tags: {ex.Message}");
        }
    }

    /// <summary>
    /// Get recent applications for a specific rule
    /// </summary>
    [HttpGet("{ruleId:guid}/recent-applications")]
    public async Task<ActionResult> GetRuleRecentApplications(Guid ruleId, [FromQuery] int limit = 5)
    {
        try
        {
            var applications = await _service.GetRuleRecentApplicationsAsync(ruleId, limit);
            return Ok(applications);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving recent applications: {ex.Message}");
        }
    }

    /// <summary>
    /// Get performance analytics for charts
    /// </summary>
    [HttpGet("{ruleId:guid}/performance")]
    public async Task<ActionResult> GetRulePerformance(Guid ruleId, [FromQuery] int days = 7)
    {
        try
        {
            var performance = await _service.GetRulePerformanceAsync(ruleId, days);
            return Ok(performance);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rule performance: {ex.Message}");
        }
    }

    /// <summary>
    /// Toggle rule status (active/inactive)
    /// </summary>
    [HttpPatch("{ruleId:guid}/toggle-status")]
    public async Task<ActionResult> ToggleRuleStatus(Guid ruleId)
    {
        try
        {
            // Get user ID from claims/authentication context
            var userId = User?.Identity?.Name ?? "system";
            var success = await _service.ToggleRuleStatusAsync(ruleId, userId);

            if (!success)
                return NotFound($"Rule with ID {ruleId} not found");

            return Ok(new { Message = "Rule status toggled successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error toggling rule status: {ex.Message}");
        }
    }

    /// <summary>
    /// Toggle tag status for a specific rule
    /// </summary>
    [HttpPatch("{ruleId:guid}/tags/{tagId:guid}/toggle")]
    public async Task<ActionResult> ToggleTagStatus(Guid ruleId, Guid tagId)
    {
        try
        {
            var userId = User?.Identity?.Name ?? "system";
            var success = await _service.ToggleTagStatusAsync(ruleId, tagId, userId);

            if (!success)
                return NotFound($"Rule-Tag association not found");

            return Ok(new { Message = "Tag status toggled successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error toggling tag status: {ex.Message}");
        }
    }

    /// <summary>
    /// Get bulk hierarchy data for multiple rules (optimization for frontend)
    /// </summary>
    [HttpPost("hierarchy/bulk")]
    public async Task<ActionResult> GetBulkRuleHierarchy([FromBody] List<Guid> ruleIds)
    {
        try
        {
            if (ruleIds == null || !ruleIds.Any())
                return BadRequest("Rule IDs are required");

            var tasks = ruleIds.Select(async ruleId => new
            {
                RuleId = ruleId,
                Hierarchy = await _service.GetRuleClientHierarchyAsync(ruleId)
            });

            var results = await Task.WhenAll(tasks);
            return Ok(results);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving bulk hierarchy: {ex.Message}");
        }
    }

    [HttpGet("{ruleId:guid}/transaction-details")]
    public async Task<ActionResult> GetRuleTransactionDetails(Guid ruleId)
    {
        try
        {
            var transactionDetails = await _service.GetRuleTransactionDetailsAsync(ruleId);
            return Ok(transactionDetails);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rule transaction details: {ex.Message}");
        }
    }
}