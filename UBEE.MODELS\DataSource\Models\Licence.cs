﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Licence: AuditEntity
{
    public string Name { get; set; }                     
    public string? Description { get; set; }              
    public List<LicenceOption> LicenceOptions { get; set; } 
    public List<ControllerServeur> ControllerServeurs { get; set; } 
    public List<Subscription> Subscriptions { get; set; }
    public List<Facture> Factures { get; set; }
}
