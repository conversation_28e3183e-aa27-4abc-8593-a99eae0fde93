﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/controller-serveur-controller")]
[ApiController]
public class ControllerServerControllerController : BaseController<ControllerServerController>
{
    private readonly IControllerServerControllerService _service;
    public ControllerServerControllerController(IControllerServerControllerService baseService) : base(baseService)
    {
        this._service = baseService as IControllerServerControllerService;
        this.UnwantedProperties += "";
        IncludesProperties = "Controller,ControllerServeur";
    }
}

