﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/Controller")]
[ApiController]
public class ControllerController : BaseController<Controller>
{
    private readonly IControllerService _service;
    public ControllerController(IControllerService baseService) : base(baseService)
    {
        this._service = baseService as IControllerService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}

