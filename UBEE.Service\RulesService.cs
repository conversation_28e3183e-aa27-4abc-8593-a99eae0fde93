﻿using UBEE.Contracts.Repository;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Service.Contracts;
using UBEE.MODELS.DataSource.DTOs;
using UBEE.Shared.GenericListModel.Filtering;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Linq;

namespace UBEE.Service;

public sealed class RulesService : BaseService<Rules>, IRulesService
{
    private readonly UBEEContext _context;

    public RulesService(IRepositoryManager unitOfWork, UBEEContext context) : base(unitOfWork)
    {
        _context = context;
    }

    /// <summary>
    /// Retrieves a paginated comprehensive list of all active rules, including statistics and tags.
    /// </summary>
    /// <param name="lister">Pagination, sorting, and filtering parameters</param>
    /// <returns>A Pager with RuleComprehensiveDto objects.</returns>
    public async Task<Pager<RuleComprehensiveDto>> GetRulesComprehensiveAsync(Lister<RuleComprehensiveDto> lister)
    {
        // Get the base data using raw SQL and convert to queryable
        var allRules = await GetAllRulesFromDatabase();
        var query = allRules.AsQueryable();

        // Apply filtering if provided
        if (lister.FilterParams != null && lister.FilterParams.Any())
        {
            query = query.Where(lister.FilterParams);
        }

        // Get total count before pagination
        var totalCount = query.Count();

        // Apply sorting
        if (lister.SortParams != null && lister.SortParams.Any())
        {
            query = ApplySorting(query, lister.SortParams);
        }
        else
        {
            // Default sorting
            query = query.OrderBy(r => r.Priority).ThenByDescending(r => r.RuleCreatedAt);
        }

        // Apply pagination
        var pageSize = lister.Pagination?.PageSize ?? 10;
        var currentPage = lister.Pagination?.CurrentPage ?? 1;
        var skip = (currentPage - 1) * pageSize;

        var pagedData = query.Skip(skip).Take(pageSize).ToList();

        // Create pagination info
        var pagination = new Pagination
        {
            CurrentPage = currentPage,
            PageSize = pageSize,
            TotalElement = totalCount,
            PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
            IsFirst = currentPage == 1,
            IsLast = currentPage >= Math.Ceiling(totalCount / (double)pageSize),
            StartIndex = skip + 1
        };

        return new Pager<RuleComprehensiveDto>
        {
            Content = pagedData,
            Lister = new Lister<RuleComprehensiveDto>
            {
                Pagination = pagination,
                SortParams = lister.SortParams,
                FilterParams = lister.FilterParams
            }
        };
    }

    /// <summary>
    /// Searches for rules with pagination, sorting, and filtering.
    /// </summary>
    /// <param name="rulesLister">Search and pagination parameters</param>
    /// <returns>A Pager with matching RuleComprehensiveDto objects.</returns>
    public async Task<Pager<RuleComprehensiveDto>> SearchRulesAsync(RulesLister rulesLister)
    {
        var searchTerm = rulesLister.SearchTerm?.Trim();

        if (string.IsNullOrEmpty(searchTerm))
        {
            return await GetRulesComprehensiveAsync(rulesLister);
        }

        // Get search results using raw SQL
        var searchResults = await GetSearchResultsFromDatabase(searchTerm, rulesLister.IncludeInactive);
        var query = searchResults.AsQueryable();

        // Apply additional filtering if provided
        if (rulesLister.FilterParams != null && rulesLister.FilterParams.Any())
        {
            query = query.Where(rulesLister.FilterParams);
        }

        // Get total count
        var totalCount = query.Count();

        // Apply sorting
        if (rulesLister.SortParams != null && rulesLister.SortParams.Any())
        {
            query = ApplySorting(query, rulesLister.SortParams);
        }
        else
        {
            query = query.OrderBy(r => r.Priority).ThenByDescending(r => r.RuleCreatedAt);
        }

        // Apply pagination
        var pageSize = rulesLister.Pagination?.PageSize ?? 10;
        var currentPage = rulesLister.Pagination?.CurrentPage ?? 1;
        var skip = (currentPage - 1) * pageSize;

        var pagedData = query.Skip(skip).Take(pageSize).ToList();

        // Create pagination info
        var pagination = new Pagination
        {
            CurrentPage = currentPage,
            PageSize = pageSize,
            TotalElement = totalCount,
            PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
            IsFirst = currentPage == 1,
            IsLast = currentPage >= Math.Ceiling(totalCount / (double)pageSize),
            StartIndex = skip + 1
        };

        return new Pager<RuleComprehensiveDto>
        {
            Content = pagedData,
            Lister = new RulesLister
            {
                Pagination = pagination,
                SortParams = rulesLister.SortParams,
                FilterParams = rulesLister.FilterParams,
                SearchTerm = searchTerm,
                IncludeInactive = rulesLister.IncludeInactive
            }
        };
    }

    /// <summary>
    /// Retrieves a comprehensive list of all active rules without pagination (for backward compatibility).
    /// </summary>
    /// <returns>A collection of RuleComprehensiveDto objects.</returns>
    public async Task<IEnumerable<RuleComprehensiveDto>> GetRulesComprehensiveAsync()
    {
        var query = @"
            SELECT r.RuleId, r.Enabled, r.Priority, r.RawData, r.RuleCreatedAt, r.RuleLastUpdatedAt,
                   r.TotalApplications, r.TotalClients, r.TotalSites, r.TotalLocals, r.LastTriggered,
                   r.SuccessfulApplications, r.FailedApplications, r.Status,
                   ISNULL(t.TagsString, '') as TagsString
            FROM vw_RulesComprehensive r
            LEFT JOIN vw_RulesWithTagsString t ON r.RuleId = t.RuleId
            ORDER BY r.Priority, r.RuleCreatedAt DESC";

        var result = await _context.Database.SqlQueryRaw<RuleComprehensiveDto>(query).ToListAsync();
        return result;
    }

    /// <summary>
    /// Searches for rules based on a search term (backward compatibility).
    /// </summary>
    /// <param name="searchTerm">The term to search for.</param>
    /// <returns>A collection of RuleComprehensiveDto objects matching the search criteria.</returns>
    public async Task<IEnumerable<RuleComprehensiveDto>> SearchRulesAsync(string searchTerm)
    {
        var query = @"
            SELECT DISTINCT r.RuleId, r.Enabled, r.Priority, r.RawData, r.RuleCreatedAt, r.RuleLastUpdatedAt,
                            r.TotalApplications, r.TotalClients, r.TotalSites, r.TotalLocals, r.LastTriggered,
                            r.SuccessfulApplications, r.FailedApplications, r.Status,
                            ISNULL(t.TagsString, '') as TagsString
            FROM vw_RulesComprehensive r
            LEFT JOIN vw_RulesWithTagsString t ON r.RuleId = t.RuleId
            WHERE r.RawData LIKE {0}
               OR t.TagsString LIKE {0}
            ORDER BY r.Priority, r.RuleCreatedAt DESC";

        var searchPattern = $"%{searchTerm}%";
        var result = await _context.Database.SqlQueryRaw<RuleComprehensiveDto>(query, searchPattern).ToListAsync();
        return result;
    }

    /// <summary>
    /// Private helper method to get all rules from database using raw SQL.
    /// </summary>
    private async Task<List<RuleComprehensiveDto>> GetAllRulesFromDatabase()
    {
        var query = @"
        SELECT 
            r.RuleId, 
            r.Enabled, 
            r.RuleSummary,
            r.Priority, 
            r.RawData, 
            r.RuleCreatedAt, 
            r.RuleLastUpdatedAt,
            r.TotalApplications, 
            r.TotalClients, 
            r.TotalSites, 
            r.TotalLocals, 
            r.LastTriggered,
            r.SuccessfulApplications, 
            r.FailedApplications, 
            r.Status,
            ISNULL(t.TagsString, '') as TagsString
        FROM vw_RulesComprehensive r
        LEFT JOIN vw_RulesWithTagsString t ON r.RuleId = t.RuleId";

        return await _context.Database.SqlQueryRaw<RuleComprehensiveDto>(query).ToListAsync();
    }


    /// <summary>
    /// Private helper method to get search results from database using raw SQL.
    /// </summary>
    private async Task<List<RuleComprehensiveDto>> GetSearchResultsFromDatabase(string searchTerm, bool includeInactive)
    {
        var searchPattern = $"%{searchTerm}%";
        var statusFilter = includeInactive ? "" : "AND r.Status = 'active'";

        var query = $@"
            SELECT DISTINCT r.RuleId, r.Enabled, r.Priority, r.RawData, r.RuleCreatedAt, r.RuleLastUpdatedAt,
                            r.TotalApplications, r.TotalClients, r.TotalSites, r.TotalLocals, r.LastTriggered,
                            r.SuccessfulApplications, r.FailedApplications, r.Status,
                            ISNULL(t.TagsString, '') as TagsString
            FROM vw_RulesComprehensive r
            LEFT JOIN vw_RulesWithTagsString t ON r.RuleId = t.RuleId
            WHERE (r.RawData LIKE {{0}} OR t.TagsString LIKE {{0}})
            {statusFilter}";

        return await _context.Database.SqlQueryRaw<RuleComprehensiveDto>(query, searchPattern).ToListAsync();
    }

    /// <summary>
    /// Applies sorting to the query based on sort parameters.
    /// </summary>
    /// <param name="query">The base query</param>
    /// <param name="sortParams">Sort parameters</param>
    /// <returns>Sorted query</returns>
    private IQueryable<RuleComprehensiveDto> ApplySorting(IQueryable<RuleComprehensiveDto> query, List<Sorting> sortParams)
    {
        IOrderedQueryable<RuleComprehensiveDto>? orderedQuery = null;

        foreach (var sort in sortParams)
        {
            var isDescending = sort.Sort?.ToUpper() == "DESC";

            switch (sort.Column?.ToLower())
            {
                case "priority":
                    orderedQuery = orderedQuery == null
                        ? (isDescending ? query.OrderByDescending(r => r.Priority) : query.OrderBy(r => r.Priority))
                        : (isDescending ? orderedQuery.ThenByDescending(r => r.Priority) : orderedQuery.ThenBy(r => r.Priority));
                    break;
                case "rulecreatedat":
                case "createdat":
                    orderedQuery = orderedQuery == null
                        ? (isDescending ? query.OrderByDescending(r => r.RuleCreatedAt) : query.OrderBy(r => r.RuleCreatedAt))
                        : (isDescending ? orderedQuery.ThenByDescending(r => r.RuleCreatedAt) : orderedQuery.ThenBy(r => r.RuleCreatedAt));
                    break;
                case "rulelastupdatedat":
                case "lastupdated":
                    orderedQuery = orderedQuery == null
                        ? (isDescending ? query.OrderByDescending(r => r.RuleLastUpdatedAt) : query.OrderBy(r => r.RuleLastUpdatedAt))
                        : (isDescending ? orderedQuery.ThenByDescending(r => r.RuleLastUpdatedAt) : orderedQuery.ThenBy(r => r.RuleLastUpdatedAt));
                    break;
                case "status":
                    orderedQuery = orderedQuery == null
                        ? (isDescending ? query.OrderByDescending(r => r.Status) : query.OrderBy(r => r.Status))
                        : (isDescending ? orderedQuery.ThenByDescending(r => r.Status) : orderedQuery.ThenBy(r => r.Status));
                    break;
                case "totalapplications":
                case "applications":
                    orderedQuery = orderedQuery == null
                        ? (isDescending ? query.OrderByDescending(r => r.TotalApplications) : query.OrderBy(r => r.TotalApplications))
                        : (isDescending ? orderedQuery.ThenByDescending(r => r.TotalApplications) : orderedQuery.ThenBy(r => r.TotalApplications));
                    break;
                case "lasttriggered":
                    orderedQuery = orderedQuery == null
                        ? (isDescending ? query.OrderByDescending(r => r.LastTriggered) : query.OrderBy(r => r.LastTriggered))
                        : (isDescending ? orderedQuery.ThenByDescending(r => r.LastTriggered) : orderedQuery.ThenBy(r => r.LastTriggered));
                    break;
                default:
                    // Default to Priority if column not recognized
                    orderedQuery = orderedQuery == null
                        ? query.OrderBy(r => r.Priority)
                        : orderedQuery.ThenBy(r => r.Priority);
                    break;
            }
        }

        return orderedQuery ?? query.OrderBy(r => r.Priority);
    }

    // All existing methods from the original service implementation
    public async Task<RuleComprehensiveDto> GetRuleComprehensiveByIdAsync(Guid ruleId)
    {
        var query = @"
            SELECT r.RuleId, r.Enabled, r.Priority, r.RawData, r.RuleCreatedAt, r.RuleLastUpdatedAt,
                   r.TotalApplications, r.TotalClients, r.TotalSites, r.TotalLocals, r.LastTriggered,
                   r.SuccessfulApplications, r.FailedApplications, r.Status,
                   ISNULL(t.TagsString, '') as TagsString
            FROM vw_RulesComprehensive r
            LEFT JOIN vw_RulesWithTagsString t ON r.RuleId = t.RuleId
            WHERE r.RuleId = {0}";

        var result = await _context.Database.SqlQueryRaw<RuleComprehensiveDto>(query, ruleId).FirstOrDefaultAsync();
        return result;
    }

    public async Task<IEnumerable<RuleClientHierarchyDto>> GetRuleClientHierarchyAsync(Guid ruleId)
    {
        var query = @"
            SELECT * FROM vw_RulesClientHierarchy
            WHERE RuleId = {0}
            ORDER BY ClientId, SiteId, LocalId";

        var result = await _context.Database.SqlQueryRaw<RuleClientHierarchyDto>(query, ruleId).ToListAsync();
        return result;
    }

    public async Task<IEnumerable<RuleWithTagDto>> GetRuleTagsAsync(Guid ruleId)
    {
        var query = @"
            SELECT RuleId, Enabled, Priority, RawData, CreatedAt, LastUpdatedAt,
                   TagId, TagCreatedAt, TagName, TagIsActive
            FROM vw_RulesWithTags
            WHERE RuleId = {0}";

        var result = await _context.Database.SqlQueryRaw<RuleWithTagDto>(query, ruleId).ToListAsync();
        return result;
    }

public async Task<IEnumerable<RuleRecentApplicationDto>> GetRuleRecentApplicationsAsync(Guid ruleId, int limit = 5)
{
    // Use a more flexible query with explicit casting
    var query = @"
        SELECT 
            RuleId, 
            TransactionId, 
            ApplicationTimestamp, 
            CAST(1 AS BIT) as ApplicationSuccess, 
            LocalName, 
            SiteName, 
            RowNum
        FROM vw_RulesRecentApplications
        WHERE RuleId = {0} AND RowNum <= {1}
        ORDER BY ApplicationTimestamp DESC";

    var result = await _context.Database.SqlQueryRaw<RuleRecentApplicationDto>(query, ruleId, limit).ToListAsync();
    return result;
}

    public async Task<IEnumerable<RulePerformanceAnalyticDto>> GetRulePerformanceAsync(Guid ruleId, int days = 7)
    {
        var query = @"
            SELECT RuleId, ApplicationDate, ApplicationHour, ApplicationDayOfWeek,
                   TotalApplications, SuccessfulApplications, FailedApplications,
                   AvgExecutionTimeMs, LocalId, LocalName
            FROM vw_RulesPerformanceAnalytics
            WHERE RuleId = {0}
              AND ApplicationDate >= DATEADD(DAY, -{1}, GETDATE())
            ORDER BY ApplicationDate DESC, ApplicationHour";

        var result = await _context.Database.SqlQueryRaw<RulePerformanceAnalyticDto>(query, ruleId, days).ToListAsync();
        return result;
    }

    public async Task<bool> ToggleRuleStatusAsync(Guid ruleId, string userId)
    {
        var query = @"
            UPDATE Rules
            SET Enabled = CASE WHEN Enabled = 1 THEN 0 ELSE 1 END,
                LastUpdatedAt = GETDATE(),
                LastUpdatedBy = {1}
            WHERE Id = {0} AND DeletedAt IS NULL";

        var rowsAffected = await _context.Database.ExecuteSqlRawAsync(query, ruleId, userId);
        return rowsAffected > 0;
    }

    public async Task<bool> ToggleTagStatusAsync(Guid ruleId, Guid tagId, string userId)
    {
        var query = @"
            UPDATE RuleTag
            SET IsActive = CASE WHEN IsActive = 1 THEN 0 ELSE 1 END,
                LastUpdatedAt = GETDATE(),
                LastUpdatedBy = {2}
            WHERE IdRule = {0} AND IdTag = {1} AND DeletedAt IS NULL";

        var rowsAffected = await _context.Database.ExecuteSqlRawAsync(query, ruleId, tagId, userId);
        return rowsAffected > 0;
    }

    public async Task<IEnumerable<RuleTransactionDetailDto>> GetRuleTransactionDetailsAsync(Guid ruleId)
    {
        var query = @"
        SELECT DISTINCT 
            rd.RuleId, 
            rd.RuleEnabled,
            rd.TransactionId, 
            rd.ControllerInControl,
            rd.ControllerId, 
            rd.ControllerIdController, 
            rd.ControllerLocalId,
            rd.TransactionCreatedAt, 
            rd.TransactionCreatedBy,
            rd.TransactionLastUpdatedAt, 
            rd.TransactionLastUpdatedBy,
            rd.RuleTransactionId, 
            rd.ApplicationTimestamp,
            rd.ApplicationCreatedBy, 
            rd.ApplicationLastUpdatedBy,
            rd.LocalName, 
            rd.SiteName, 
            rd.SiteAddress,
            rd.ControllerBaseTopic
        FROM vw_RulesTransactionDetails rd
        WHERE rd.RuleId = {0}
        ORDER BY rd.ApplicationTimestamp DESC";

        var result = await _context.Database.SqlQueryRaw<RuleTransactionDetailDto>(query, ruleId).ToListAsync();
        return result;
    }
}