﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/licence")]
[ApiController]
public class LicenceController : BaseController<Licence>
{
    private readonly ILicenceService _service;
    public LicenceController(ILicenceService baseService) : base(baseService)
    {
        this._service = baseService as ILicenceService;
        this.UnwantedProperties += "";
        IncludesProperties = "ControllerServeurs,LicenceOptions.Option";
    }
}

