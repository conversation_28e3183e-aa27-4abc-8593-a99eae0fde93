﻿namespace UBEE.MODELS.DataSource;
using Microsoft.EntityFrameworkCore;
using System.Reflection;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using UBEE.MODELS.Common.Models;
using UBEE.Shared.Views;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;

public class UBEEContext: IdentityDbContext<IdentityUser>
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    public DbSet<ClientLicenceControllerView> ClientLicenceControllerViews { get; set; } = null!;
    public DbSet<vwCapteurClientSite> vwsCapteurClientSite { get; set; } = null!;
    public UBEEContext(DbContextOptions<UBEEContext> options, IHttpContextAccessor httpContextAccessor)
       : base(options)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetAssembly(typeof(UBEEContext)));
        modelBuilder.Entity<ClientLicenceControllerView>(entity => {
            entity.ToView("ClientLicenceControllersView");
        modelBuilder.Entity<vwCapteurClientSite>(entity => {
            entity.ToView("vwCapteurClientSite");
            entity.HasNoKey();
        });
            entity.HasNoKey();
        });
        base.OnModelCreating(modelBuilder);
    }

    public override int SaveChanges()
    {
        AddTimestamps();
        return base.SaveChanges();
    }

    private void ReleaseEntities()
    {
        foreach (var dbEntityEntry in ChangeTracker.Entries())
        {
            dbEntityEntry.State = EntityState.Unchanged;
        }
    }
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        AddTimestamps();
        return await base.SaveChangesAsync();
    }

    private async void AddTimestamps()
    {
        var entities = ChangeTracker.Entries()
            .Where(x => (x.Entity is AuditEntity || x.Entity is Audit) && (x.State == EntityState.Added || x.State == EntityState.Modified));

        foreach (var entity in entities)
        {
            string nameClaim = null;
            var httpContextAccessor = this.GetService<IHttpContextAccessor>();
            if (httpContextAccessor is not null)
            {
                var claims = httpContextAccessor.HttpContext.User.Identities.FirstOrDefault().Claims.ToList();

                if (claims.Count > 0)
                {
                    nameClaim = claims[3].Value;
                }
            }


            var now = DateTime.UtcNow; // current datetime
            if (entity.Entity is AuditEntity entity1)
            {
                if (entity.State == EntityState.Added)
                {
                    if (!((AuditEntity)entity.Entity).CreatedAt.HasValue)
                        entity1.CreatedAt = now;
                    if (string.IsNullOrWhiteSpace(((AuditEntity)entity.Entity).CreatedBy))
                    {
                        var userName = nameClaim;
                        entity1.CreatedBy = userName == null ? null : userName;
                    }
                }
                else if (((AuditEntity)entity.Entity).LastUpdatedAt == null)
                {
                    if (!((AuditEntity)entity.Entity).LastUpdatedAt.HasValue)
                        entity1.LastUpdatedAt = now;
                    if (string.IsNullOrWhiteSpace(((AuditEntity)entity.Entity).LastUpdatedBy))
                    {
                        var userName = nameClaim;
                        entity1.LastUpdatedBy = userName == null ? null : userName;
                    }
                }
            }
            else if (entity.Entity is Audit audit)
            {
                if (entity.State == EntityState.Added)
                {
                    if (((Audit)entity.Entity).CreatedAt == null)
                        audit.CreatedAt = now;
                    if (string.IsNullOrWhiteSpace(((Audit)entity.Entity).CreatedBy))
                    {
                        var userName = nameClaim;
                        audit.CreatedBy = userName == null ? null : userName;
                    }
                }
                else if (((Audit)entity.Entity).LastUpdatedAt == null)
                {
                    if (!((Audit)entity.Entity).LastUpdatedAt.HasValue)
                        audit.LastUpdatedAt = now;
                    if (string.IsNullOrWhiteSpace(((Audit)entity.Entity).LastUpdatedBy))
                    {
                        var userName = nameClaim;
                        audit.LastUpdatedBy = userName == null ? null : userName;
                    }
                }
            }

        }
    }
}

