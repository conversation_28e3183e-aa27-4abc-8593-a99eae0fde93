﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/transaction")]
[ApiController]
public class TransactionController : BaseController<Transaction>
{
    private readonly ITransactionService _service;
    public TransactionController(ITransactionService baseService) : base(baseService)
    {
        this._service = baseService as ITransactionService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}
