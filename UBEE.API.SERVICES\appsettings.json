{
  //"https_port": 443,
  "ConnectionStrings": {
    //"sqlConnection": "Server=Mohammed-Laptop\\SQLEXPRESS;Database=UBBEEDB;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=True;"
    //"sqlConnection": "Server=.\\SQLEXPRESS;Database=UBBEEDB;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=True;",
    "sqlConnection": "Server=**************;Database=UBBEEDB;user=userIOT;password=********************;MultipleActiveResultSets=true;TrustServerCertificate=True;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "JWT": {
    "ValidAudience": "https:localhost:5001;http://localhost:5000",
    "ValidIssuer": "https:localhost:5001;http://localhost:5000",
    "Secret": "kV%&c7$J}[IThk&}y'y[3NJCq}1=ze%]AIDe$WFeRMi_Jm)>`Mx+S~'?sZR54ev"
  },
  "EmailConfiguration": {
    "From": "",
    "SmtpServer": "smtp.gmail.com",
    "Port": 465,
    "Username": "",
    "Password": ""
  },
  "AllowedHosts": "*",
  "Groq": {
    "BaseUrl": "https://api.groq.com/openai/v1/chat/completions",
    "ApiKey": "********************************************************",
    "Model": "llama3-8b-8192"
  }

}
