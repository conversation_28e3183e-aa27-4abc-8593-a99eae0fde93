﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;

using UBEE.Service.Contracts;
using UBEE.Shared.DataTransferObjects.Dto;

namespace UBEE.API.SERVICES.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ImagesController : ControllerBase
{
    private readonly UBEEContext _context;
    private readonly IImageService _imageService;

    public ImagesController(UBEEContext context, IImageService imageService)
    {
        _context = context;
        this._imageService = imageService;
    }

    [HttpPost("upload/client")]
    public async Task<IActionResult> UploadImageFromBytes([FromBody] UploadImageDto dto)
    {
        if (dto?.ImageData == null || dto.ImageData.Length == 0)
            return BadRequest("Image data is empty");

        try
        {
            using var inputStream = new MemoryStream(dto.ImageData);

            var client = await _context.Set<Client>().FirstOrDefaultAsync(s => s.Id == dto.Id);
            if (client == null)
                return NotFound("Client not found");

            if(client.ClientLogo != null)
            {
                var resizedImage = await this._imageService.ResizeAndOptimizeAsync(inputStream);
                client.ClientLogo = resizedImage;
                _context.Entry(client).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                return Ok(new { image = resizedImage });
            }
            return Ok();
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Image processing failed: {ex.Message}");
        }
    }

    [HttpPost("download/client")]
    public async Task<IActionResult> DownloadImageClientFromBytes(Guid Id)
    {
        try
        {
            var client = await _context.Set<Client>().FirstOrDefaultAsync(s => s.Id == Id);
            if (client == null)
                return NotFound("Client not found");

            return Ok(new { image = client.ClientLogo });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Image processing failed: {ex.Message}");
        }
    }

    [HttpPost("upload/local")]
    public async Task<IActionResult> UploadImageLocal([FromBody] UploadImageDto dto)
    {
        if (dto?.ImageData == null || dto.ImageData.Length == 0)
            return BadRequest("Image data is empty");

        try
        {
            using var inputStream = new MemoryStream(dto.ImageData);

            var local = await _context.Set<Local>().FirstOrDefaultAsync(s => s.Id == dto.Id);
            if (local == null)
                return NotFound("local not found");

            var resizedImage = await this._imageService.ResizeAndOptimizeAsync(inputStream);
            local.ImageLocal = resizedImage;
            _context.Entry(local).State = EntityState.Modified;

            await _context.SaveChangesAsync();

            return Ok(new { image = resizedImage });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Image processing failed: {ex.Message}");
        }
    }

    [HttpPost("download/local")]
    public async Task<IActionResult> DownloadImageFromBytes(Guid Id)
    {
        try
        {
            var local = await _context.Set<Local>().FirstOrDefaultAsync(s => s.Id == Id);
            if (local == null)
                return NotFound("local not found");

            return Ok(new { image = local.ImageLocal });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Image processing failed: {ex.Message}");
        }
    }

    [HttpPost("upload/site")]
    public async Task<IActionResult> UploadImageSite([FromBody] UploadImageDto dto)
    {
        if (dto?.ImageData == null || dto.ImageData.Length == 0)
            return BadRequest("Image data is empty");

        try
        {
            using var inputStream = new MemoryStream(dto.ImageData);

            var site = await _context.Set<Site>().FirstOrDefaultAsync(s => s.Id == dto.Id);
            if (site == null)
                return NotFound("Client not found");

            var resizedImage = await this._imageService.ResizeAndOptimizeAsync(inputStream);
            site.Image = resizedImage;
            _context.Entry(site).State = EntityState.Modified;

            await _context.SaveChangesAsync();

            return Ok(new { image = resizedImage });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Image processing failed: {ex.Message}");
        }
    }

    [HttpPost("download/site")]
    public async Task<IActionResult> DownloadImageSiteFromBytes(Guid Id)
    {
        try
        {
            var site = await _context.Set<Site>().FirstOrDefaultAsync(s => s.Id == Id);
            if (site == null)
                return NotFound("local not found");

            return Ok(new { image = site.Image });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Image processing failed: {ex.Message}");
        }
    }

}
