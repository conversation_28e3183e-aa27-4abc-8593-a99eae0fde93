﻿using Microsoft.EntityFrameworkCore;
using System.Collections;
using System.Collections.ObjectModel;
using System.Linq.Expressions;
using System.Reflection;
using UBEE.Contracts.Repository.Base;
using UBEE.MODELS.Common.Models;
using UBEE.MODELS.DataSource;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Repository.Repository.Base;
public class GenericRepository
{
}

/// <summary>
/// The Generic Repository Class
/// </summary>
/// <typeparam name="TEntity">TEntity</typeparam>
public class GenericRepository<TEntity> : GenericRepository, IGenericRepository<TEntity> where TEntity : BaseClass
{
    /// <summary>
    /// Gets a all Type instances matching the specified class name with just non-namespace qualified class name.
    /// </summary>
    /// <param name="className">Name of the class sought.</param>
    /// <returns>Types that have the class name specified. They may not be in the same namespace.</returns>
    public static Type[] GetTypeByName(string className)
    {
        List<Type> returnVal = new List<Type>();

        foreach (Assembly a in AppDomain.CurrentDomain.GetAssemblies())
        {
            Type[] assemblyTypes = a.GetTypes();
            for (int j = 0; j < assemblyTypes.Length; j++)
            {
                if (assemblyTypes[j].Name == className)
                {
                    returnVal.Add(assemblyTypes[j]);
                }
            }
        }

        return returnVal.ToArray();
    }

    public async Task<int> GetCountAsync(Expression<Func<TEntity, bool>> expression)
    {
        return await DbSet
            .Where(expression)
            .CountAsync();
    }
    /// <summary>
    /// The internat context
    /// </summary>
    protected UBEEContext Context;

    /// <summary>
    /// The internal dbset
    /// </summary>
    protected DbSet<TEntity> DbSet;

    /// <summary>
    /// The generic repository constructor
    /// </summary>
    /// <param name="context"></param>
    public GenericRepository(UBEEContext context)
    {
        Context = context;
        DbSet = Context.Set<TEntity>();
    }

    /// <summary>
    /// The generic repository constructor
    /// </summary>
    //public GenericRepository()
    //{
    //    Context = new PmContext();
    //    DbSet = Context.Set<TEntity>();
    //}

    public async Task<int> Count()
    {
        return await DbSet.CountAsync();
    }

    public virtual async Task<TEntity> GetByChildId(Guid id, string includeProperties, string entityName)
    {
        if (entityName != null && typeof(TEntity).GetProperty(entityName) == null) return null;
        var param = Expression.Parameter(typeof(TEntity));
        var left = Expression.Convert(Expression.Property(param, "Id" + entityName), typeof(Guid));
        var body = Expression.Equal((Expression)left, Expression.Constant(id));

        var entity = DbSet.Where(Expression.Lambda<Func<TEntity, bool>>(body, param));
        if (includeProperties == null) return await entity.LastOrDefaultAsync();
        entity = includeProperties.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Aggregate(entity, (current, includeProperty) => current.Include(includeProperty));
        return await entity.LastOrDefaultAsync();
    }

    public virtual async Task<IEnumerable<TEntity>> GetAllByChildId(Guid id, string entityName, string orderBy, bool orderDesc, string includeProperties)
    {


        if (typeof(TEntity).GetProperty(entityName) == null) return null;
        var param = Expression.Parameter(typeof(TEntity));
        var left = Expression.Convert(Expression.Property(param, "Id" + entityName), typeof(Guid));
        var body = Expression.Equal((Expression)left, Expression.Constant(id));

        var entities = DbSet.Where(Expression.Lambda<Func<TEntity, bool>>(body, param));


        foreach (var includeProperty in includeProperties.Split
            (new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
        {
            entities = entities.Include(includeProperty);
        }

        var list = await entities.ToListAsync();
        if (orderBy != "")
        {
            list = [.. (orderDesc
                ? list.OrderByDescending(t => t.GetType().GetProperty(orderBy)?.GetValue(t, null))
                : list.OrderByDescending(t => t.GetType().GetProperty(orderBy)?.GetValue(t, null)))];
        }
        return list;
    }

    public async Task<Pager<TEntity>> GetPages(Lister<TEntity> lister, string includeProperties = "", IQueryable<TEntity> query = null)
    {
        Pager<TEntity> pager = new Pager<TEntity>();
        if (query == null)
        {
            query = DbSet;
        }

        var idInfo = typeof(TEntity).GetProperty("Id");
        /*var properties = includeProperties.Split(new char[] {','});
        foreach (var property in properties)
        {
            var entity = property;
            var nestedProperties = property.Split(new char[] {'.'});
            if (nestedProperties.Length > 1)
            {
                entity = nestedProperties[0];
            }

            var types = GetTypeByName(entity);
            foreach (var type in types)
            {
                if(type != null && Context.Set(type) != null)
                    Context.Set(type).Load();
            }
        }*/
        query = includeProperties.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Aggregate(query, (current, includeProperty) => current.Include(includeProperty));

        if (!(lister.SortParams is null))
        {
            IOrderedEnumerable<TEntity> orderedList = null;

            using (var e = lister.SortParams.GetEnumerator())
            {
                if (e.MoveNext() && e.Current?.Column != null)
                {
                    var paramInfo = typeof(TEntity).GetProperty(e.Current.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    if (paramInfo != null && typeof(BaseClass).IsAssignableFrom(paramInfo.PropertyType?.BaseType))
                    {
                        paramInfo = paramInfo.GetType().GetProperty("Libelle") == null
                            ? typeof(TEntity).GetProperty("Id" + paramInfo.Name,
                                BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance)
                            : typeof(TEntity).GetProperty(paramInfo.Name + ".Libelle",
                                BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    }


                    switch (e.Current.Sort)
                    {
                        case "asc":
                            orderedList = orderedList == null
                                ? query.AsEnumerable().OrderBy(x => paramInfo?.GetValue(x, null))
                                : orderedList.ThenBy(x =>
                                    paramInfo?.GetValue(x, null));
                            break;
                        case "desc":
                            orderedList = orderedList == null
                                ? query.AsEnumerable().OrderByDescending(x => paramInfo?.GetValue(x, null))
                                : orderedList.ThenByDescending(x =>
                                    paramInfo?.GetValue(x, null));
                            break;
                        default: throw new ArgumentException("Bad ordering operator.");
                    }


                }

                if (orderedList != null) query = orderedList.AsQueryable();
            }

        }

        if (!(lister.FilterParams is null))
        {
            var wheres = new Filtering.WhereParams[lister.FilterParams.Count()];

            for (int i = 0; i < wheres.Length; i++)
            {
                wheres[i] = lister.FilterParams[i];
            }

            query = query.Where(wheres);
        }
        lister.Pagination.TotalElement = query.Count();
        if (lister.Pagination != null)
        {
            var skip = (lister.Pagination.CurrentPage - lister.Pagination.StartIndex) * lister.Pagination.PageSize;
            if (query.Count() <= lister.Pagination.PageSize)
            {
                lister.Pagination.CurrentPage = lister.Pagination.StartIndex;
                skip = 0;
            }

            if (skip > 0)
                query = lister.SortParams == null || !lister.SortParams.Any() ? query.AsEnumerable().OrderBy(x => idInfo?.GetValue(x, null)).AsQueryable().Skip(skip) :
                    query.Skip(skip);
            var take = query.Count() < lister.Pagination.PageSize ? query.Count() : lister.Pagination.PageSize;

            lister.Pagination.PageCount = !query.Any() ? 1 : lister.Pagination.TotalElement % lister.Pagination.PageSize != 0
                ? (lister.Pagination.TotalElement / lister.Pagination.PageSize) + 1
                : (lister.Pagination.TotalElement / lister.Pagination.PageSize);

            if (take > 0)
                query = query.Take(take);

            lister.Pagination.IsLast = lister.Pagination.CurrentPage == lister.Pagination.PageCount;
            lister.Pagination.IsFirst = lister.Pagination.CurrentPage == 1;
        }



        pager.Content = query.ToList();

        pager.Lister = lister;
        return pager;


    }


    /// <summary>
    /// The generic get method
    /// </summary>
    /// <param name="filter">filter</param>
    /// <param name="orderBy">orderBy</param>
    /// <param name="includeProperties">include Properties</param>
    /// <returns>IEnumerable<TEntity></returns>
    public virtual async Task<IEnumerable<TEntity>> Get(
        Expression<Func<TEntity, bool>> filter = null,
        Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
        string includeProperties = "", int skip = 0, int take = 0)
    {
        IQueryable<TEntity> query = DbSet;

        if (filter != null)
        {
            query = query.Where(filter);
        }

        foreach (var includeProperty in includeProperties.Split
            (new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
        {
            query = query.Include(includeProperty);
        }

        if (skip >= 0 && take > 0)
        {
            return orderBy != null
                ? orderBy(query).Skip(skip).Take(take).ToList()
                : query.OrderBy(x => 1 == 1).Skip(skip).Take(take).ToList();
        }
        var result = orderBy != null ? orderBy(query).ToList() : [.. query];

        return result.AsEnumerable();
    }

    public async Task<TEntity> GetById(object id)
    {
        return await DbSet.FindAsync(id);
    }

    public async Task<TEntity> GetFirstOrDefault()
    {
        return await DbSet.FirstOrDefaultAsync();
    }


    /// <summary>
    /// The generic get by id method
    /// </summary>
    /// <param name="id">identifier</param>
    /// <param name="includeProperties"></param>
    /// <returns>TEntity</returns>
    public virtual async Task<TEntity> GetOne(object id, string includeProperties)
    {
        IQueryable<TEntity> query = DbSet;
        if (query.Any())
        {
            query = GetQueryEntity(query, id);
            foreach (var includeProperty in includeProperties.Split
                (new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                query = query.Include(includeProperty);
            }
        }

        return await query.FirstOrDefaultAsync();
    }

    private IQueryable<TEntity> GetQueryEntity(IQueryable<TEntity> query, object id)
    {
        var param = Expression.Parameter(typeof(TEntity));
        var body = Expression.Equal(Expression.Property(param, "Id"),
            Expression.Constant(id));

        return query.Where(Expression.Lambda<Func<TEntity, bool>>(body, param));
    }

    /// <summary>
    /// The generic insert method
    /// </summary>
    /// <param name="entity">TEntity</param>
    public virtual async Task<TEntity> Insert(TEntity entity)
    {
        try
        {
            foreach (var propertyInfo in entity?.GetType().GetProperties())
            {
                if (typeof(IEnumerable).IsAssignableFrom(propertyInfo.PropertyType))
                {
                    if (propertyInfo.GetValue(entity, null) is ICollection oldCollection)
                    {
                        //var childType = propertyInfo.PropertyType?.GetGenericArguments().Single();
                        //// UNCOMMENT
                        //var dbSet = Context.Set<TEntity>();
                        //foreach (var child in oldCollection)
                        //{
                        //    // if (dbSet.Find((child as BaseEntity)?.Id) != null)
                        //    //   Context.Entry(child).State = EntityState.Unchanged;
                        //}
                    }
                }
            }

            await DbSet.AddAsync(entity);

            return entity;

        }
        catch (Exception e)
        {
            //Context.InsertException(e);
            throw;
        }
    }

    private void GenericAttachement(TEntity entity)
    {

    }

    /// <summary>
    /// The generic delete method by identifier
    /// </summary>
    /// <param name="id">identifier</param>
    public virtual async Task<bool> Delete(object id)
    {
        Guid entityId = Guid.Parse(id.ToString());
        var entity = await DbSet.FirstOrDefaultAsync(s => s.Id == entityId);
        //int rows = await Context.Database.ExecuteSqlRawAsync(
        //    "EXEC DeleteEntityAndRelated @ParentTable = {0}, @ParentId = {1}",
        //    tbl,
        //    entityId
        //);

        if (entity == null)
        {
            return false;
        }

        Context.Entry(entity).State = EntityState.Deleted;
        Context.Remove(entity);

        int rows = await Context.SaveChangesAsync();

        return rows > 0;

    }

    /// <summary>
    /// The generic delete method 
    /// </summary>
    /// <param name="entityToDelete">entit y To Delete</param>
    public virtual TEntity Delete(TEntity entityToDelete)
    {
        if (Context.Entry(entityToDelete).State == EntityState.Detached)
        {
            DbSet.Attach(entityToDelete);
        }

        var entity = entityToDelete;
        DbSet.Remove(entityToDelete);
        return entity;
    }

    /// <summary>
    /// Detach Object
    /// </summary>
    /// <param name="entityToDetach">entity To Detach</param>
    public virtual void DetachObject(TEntity entityToDetach)
    {
        Context.Entry(entityToDetach).State = EntityState.Detached;
    }

    /// <summary>
    /// The generic update method
    /// </summary>
    /// <param name="propertiesToCheck"></param>
    /// <param name="entityToUpdate">entity To Update</param>
    public virtual async Task<TEntity> Update(TEntity entityToUpdate, string propertiesToCheck)
    {
        var oldEntity = await GetOne(entityToUpdate.Id, propertiesToCheck);
        var properties = propertiesToCheck.Split
            (new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

        //if (!string.IsNullOrEmpty(propertiesToCheck))
            //foreach (var property in properties)
            //{
            //    var propertyToCheck = property.Contains(".") ? property.Substring(0, property.IndexOf(".")) : property;
            //    if (entityToUpdate.GetType().GetProperty(propertyToCheck)?.GetValue(entityToUpdate) != null ||
            //        entityToUpdate.GetType().GetProperty("Id" + propertyToCheck)
            //            ?.GetValue(entityToUpdate) != null)
            //        oldEntity = await UpdateChild(oldEntity, entityToUpdate, propertyToCheck);
            //}

        var entry = Context.Entry(oldEntity);
        if (entry.State == EntityState.Detached)
            Context.Attach(oldEntity);

        Context.Entry(oldEntity).CurrentValues.SetValues(entityToUpdate);
        Context.Entry(oldEntity).State = EntityState.Modified;
        await Context.SaveChangesAsync();

        return oldEntity;
    }

    private async Task<TEntity> UpdateChild(TEntity oldEntity, TEntity entityToUpdate, string property)
    {
        //UNCOMMENT
        var propertyType = entityToUpdate.GetType().GetProperty(property)?.PropertyType;

        if (typeof(IEnumerable).IsAssignableFrom(propertyType))
        {
            Type childType = propertyType?.GetGenericArguments().Single();
            var dbSet = Context;

            var newCollection = ((IList)
                entityToUpdate.GetType().GetProperty(property)?.GetValue(entityToUpdate, null))?.Cast<BaseClass>().ToList();
            if (oldEntity.GetType().GetProperty(property)?.GetValue(oldEntity, null) is IEnumerable oldCollection && newCollection != null)
            {
                var collection = (IList)oldCollection.Cast<BaseClass>().ToList();
                var itemAdded = false;
                foreach (var oldValue in collection)
                {
                    if (newCollection.All(e => e.Id.ToString() != oldValue.GetType().GetProperty("Id")?.GetValue(oldValue, null).ToString()))
                    {
                        dbSet.Remove(oldValue);
                    }
                }

                foreach (var newValue in newCollection)
                {
                    var oldValue = newValue.Id != Guid.Empty ? Contains(collection, newValue.Id) : null;
                    if (oldValue != null)
                    {
                        var oldChild = dbSet.Find(childType, oldValue.Id);
                        if (oldChild == null)
                        {
                            dbSet.Add(newValue);
                        }
                        else
                        {
                            Context.Entry(oldValue).CurrentValues.SetValues(newValue);
                        }
                    }
                    else
                    {
                        collection.Add(newValue);
                        if (!itemAdded)
                        {
                            itemAdded = true;
                        }
                    }
                }

                if (itemAdded)
                {
                    Type collectionType = typeof(ObservableCollection<>).MakeGenericType(childType);
                    var collect = (IList)Activator.CreateInstance(collectionType);
                    foreach (var item in collection)
                    {
                        var state = Context.Entry(item).State;
                        if (state != EntityState.Deleted)
                        {
                            collect.Add(item);
                        }
                    }
                    oldEntity.GetType().GetProperty(property)?.SetValue(oldEntity, collect);
                }
            }
            else if (newCollection != null && newCollection.Any())
            {
                if (newCollection[0].Id != Guid.Empty)
                {
                    oldCollection = new List<BaseClass>();
                    var addMethod = oldCollection.GetType()
                        .GetMethods().FirstOrDefault(m => m.Name == "Add" && m.GetParameters().Count() == 1);
                    foreach (var newEntity in newCollection)
                    {
                        if (dbSet.Find(childType, newEntity.Id) == null)
                        {
                            dbSet.Attach(newEntity);
                        }

                        addMethod?.Invoke(oldCollection, new object[] { newEntity });
                    }

                }
                else
                {
                    oldEntity.GetType().GetProperty(property)?.SetValue(oldEntity,
                        entityToUpdate.GetType().GetProperty(property)?.GetValue(entityToUpdate, null));
                }

            }
        }
        else
        {
            var oldValue = (BaseClass)oldEntity.GetType().GetProperty(property)?.GetValue(oldEntity, null);
            var newValue = (BaseClass)entityToUpdate.GetType().GetProperty(property)?.GetValue(entityToUpdate, null);
            var dbSet = Context;

            if (newValue.Id == Guid.Empty)
            {
                dbSet.Add(newValue);
                await Context.SaveChangesAsync();
            }

            var newValueId = entityToUpdate.GetType().GetProperty("Id" + property)?.GetValue(entityToUpdate);
            if (newValueId != null && newValue?.Id != Guid.Parse(newValueId.ToString()))
            {
                newValue = (BaseClass)dbSet.Find(propertyType, newValueId);
            }

            if (newValue != null && oldValue != null && newValueId == null)
            {
                newValue.Id = oldValue.Id;
                Context.Entry(oldValue).CurrentValues.SetValues(newValue);
            }
            else
            {
                dbSet = Context;

                if (newValue == null && oldValue != null)
                {
                    dbSet.Remove(oldValue);
                }

                else if (newValue != null)
                {
                    if (dbSet.Find(propertyType, newValue.Id) != null)
                        await Context.SaveChangesAsync();
                    else
                    {
                        dbSet.Add(newValue);
                    }

                    entityToUpdate.GetType().GetProperty("Id" + property)?.SetValue(entityToUpdate, newValue.Id);

                    if (newValue.GetType().GetProperty("Id" + entityToUpdate.GetType().Name)
                            ?.GetValue(newValue, null) == null)
                    {
                        newValue.GetType().GetProperty("Id" + entityToUpdate.GetType().Name)
                            ?.SetValue(newValue, entityToUpdate.Id);
                    }
                }
            }
        }

        return oldEntity;
    }

    private BaseClass Contains(IList collection, Guid id)
    {
        foreach (var item in collection)
        {
            var obj = item as BaseClass;
            if (id == obj?.Id)
                return obj;
        }
        return null;
    }
    /// <summary>
    /// The generic update values method.
    /// </summary>
    /// <param name="entityToUpdate">entity to update.</param>
    public virtual TEntity UpdateValues(TEntity entityToUpdate)
    {

        TEntity entity = DbSet.Find(entityToUpdate.Id);
        Context.Entry(entity).CurrentValues.SetValues(entityToUpdate);
        return entityToUpdate;
    }

    /// <summary>
    /// Update List of entities
    /// </summary>
    /// <param name="entityTiesUpdate">Entities to update</param>
    public virtual void Update(IEnumerable<TEntity> entityTiesUpdate)
    {

        foreach (TEntity entity in entityTiesUpdate)
        {
            DbSet.Attach(entity);
            Context.Entry(entity).State = EntityState.Modified;
        }

    }

    /// <summary>
    /// Delete List of entities
    /// </summary>
    /// <param name="entityTiesToDelete">Entities to delete</param>
    public virtual void Delete(IEnumerable<TEntity> entityTiesToDelete)
    {

        foreach (TEntity entity in entityTiesToDelete)
        {
            if (Context.Entry(entity).State == EntityState.Detached)
            {
                DbSet.Attach(entity);
            }

            DbSet.Remove(entity);
        }
    }

    /// <summary>
    /// Insert List of entities
    /// </summary>
    /// <param name="entityTiesUpdate">Entities to update</param>
    public virtual void Insert(IEnumerable<TEntity> entityTiesInsert)
    {
        DbSet.AddRange(entityTiesInsert);
    }

    public string GenerateReference(string prefix = "")
    {
        //var Object = DbSet.OrderByDescending(o => o.Reference).FirstOrDefault();
        //if (Object == null && !DbSet.Any())
        //    return prefix + "-0001";

        //if (Object != null)
        //{
        //    int number = Convert.ToInt32(Object.Reference.Split('-')[1]);

        //    number++;
        //    if (number < 10)
        //        return prefix + "-000" + number;
        //    if (number < 100)
        //        return prefix + "-00" + number;
        //    if (number < 1000)
        //        return prefix + "-0" + number;
        //    if (number < 10000)
        //        return prefix + "-" + number;
        //}

        return prefix + "-0001";
    }

    /// <summary>
    /// Gets items by query
    /// </summary>
    /// <param name="sqlQuery">query</param>
    /// <param name="parameters">params of query</param>
    /// <returns></returns>
    public virtual IEnumerable<TEntity> GetItemsByQuery(string sqlQuery, params object[] parameters)
    {
        // UNCOMMENT
        //return Context.Database.SqlQuery<T>(sqlQuery, parameters);
        return null;
    }

    public TEntity CheckIfExistByProperty(string propertyName, object value)
    {
        if (typeof(TEntity).GetProperty(propertyName) == null) return null;
        var type = value.GetType();
        var param = Expression.Parameter(typeof(TEntity));
        var left = Expression.Convert(Expression.Property(param, propertyName), type);
        var body = Expression.Equal((Expression)left, Expression.Constant(value));

        var entity = DbSet.Where(Expression.Lambda<Func<TEntity, bool>>(body, param)).SingleOrDefault();
        return entity;
    }

    public void ExecuteQueryWithoutResult(string sqlQuery, params object[] parameters)
    {
    }

    public IEnumerable<T> GetItemsCountByQuery<T>(string sqlQuery, params object[] parameters)
    {
        return null;
    }

    public async Task<IEnumerable<TEntity>> InsertRange(List<TEntity> entities)
    {
        await DbSet.AddRangeAsync(entities);
        return entities;
    }

}
