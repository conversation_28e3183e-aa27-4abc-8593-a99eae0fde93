﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/log")]
[ApiController]
public class LogController : BaseController<Log>
{
    private readonly ILogService _service;
    public LogController(ILogService baseService) : base(baseService)
    {
        this._service = baseService as ILogService;
        this.UnwantedProperties += "";
        IncludesProperties = "Transaction";
    }
}
