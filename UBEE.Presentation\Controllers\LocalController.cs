﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Presentation.Models;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/local")]
[ApiController]
public class LocalController : BaseController<Local>
{
    private readonly ILocalService _service;
    public LocalController(ILocalService baseService) : base(baseService)
    {
        this._service = baseService as ILocalService;
        this.UnwantedProperties += ",ImageLocal";
        IncludesProperties = "Site,TypeLocal";
    }

    public override Task<IActionResult> Get(Guid id)
    {
        this.IncludesProperties = "Site,TypeLocal";
        return base.Get(id);
    }

    [HttpGet("site/{IdSite}")]
    public virtual async Task<IActionResult> GetBySiteId(Guid IdSite)
    {
        UnwantedProperties = "Floor,SensorsCount,Capacity,Architecture2DImage,ImageLocal,Latitude,Longtitude,Site,TypeLocalId,TypeLocal,Transactions,CreatedAt,CreatedBy,LastUpdatedAt,LastUpdatedBy,DeletedAt,DeletedBy";
        return Ok((await this.Service.Get(s => s.IdSite == IdSite, includeProperties: "")).MarshelList(WantedProperties, UnwantedProperties));
    }

    [HttpGet("by-site/{siteId}")]
    public async Task<IActionResult> GetByClient(Guid siteId)
    {
        if (siteId == Guid.Empty)
            return BadRequest("ID site invalide");

        // On récupère les locaux filtrés par clientId, sans Include pour la version simple
        var locals = await Service.Get(e => e.IdSite == siteId);

        return Ok(locals.MarshelList(WantedProperties, UnwantedProperties));
    }
}
