﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Client : AuditEntity
{
    public string Name { get; set; } //RaisonSocial

    public string Address { get; set; } //Adresse

    public string PhoneNumber { get; set; } //Tel

    public byte[]? ClientLogo { get; set; } //LogoClient

    public string SIREN { get; set; }

    public string SIRET { get; set; }

    public string RC { get; set; }

    public string ICE { get; set; }

    public string IF { get; set; }

    public string Patente { get; set; }

    public string LegalForm { get; set; } //FormeJuridique

    public string BusinessSector { get; set; } //SecteurActivite

    public string ClientStatus { get; set; } //EtatClient

    public DateTime CompanyCreationDate { get; set; } //DateDeCreationEntreprise
    
    public string? ContactName { get; set; } //NomContact

    public string? ContactAddress { get; set; } //AdresseContact
        
    public string? ContactEmail { get; set; } //EmailContact

    public string? Brand { get; set; } //Marque

    public string? CompanyType { get; set; } //TypeEntreprise

    public int? CompanySize { get; set; } // Taille <PERSON>'Entreprise

    public string? City { get; set; } // Ville

    public string? Region { get; set; } // Region

    public string? Country { get; set; } // Pays

    public string? Filiale { get; set; } // Filiale

    public string? Status { get; set; } //  Statut

    public Guid? RecursiveClientId { get; set; }

    public int? ActiveEquipment { get; set; } // Nombre d'équipements actifs

    public int? InactiveEquipment { get; set; }  // Nombre d'équipements inactifs

    public Guid IdOrganisation { get; set; }

    public Organisation Organisation { get; set; }

    public List<Site> Sites { get; set; }

    public List<Subscription> Subscriptions { get; set; }

}
