﻿namespace UBEE.Shared.Views;

public class vwCapteurClientSite
{

    public Guid? SiteId { get; set; }

    public string? SiteName { get; set; }

    public Guid? ClientId { get; set; }

    public string? ClientName { get; set; }


    public Guid? LocalId { get; set; }

    public string? LocalName { get; set; }

    public string? RowData { get; set; }

    public DateTime? LastSeen { get; set; }

    public string? Topic { get; set; }

    public string? State { get; set; }

    public string? BaseTopic { get; set; }

}