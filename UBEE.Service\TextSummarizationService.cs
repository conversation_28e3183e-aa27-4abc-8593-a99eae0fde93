﻿using UBEE.Service.Contracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text;
using System.Net.Http.Headers;
using System.Data;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.Service;

public sealed class TextSummarizationService : ITextSummarizationService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<TextSummarizationService> _logger;
    private readonly ITypeCapteurService _typeCapteurService;
    private readonly IVariablesService _variablesService;

    public TextSummarizationService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<TextSummarizationService> logger,
        ITypeCapteurService typeCapteurService,
        IVariablesService variablesService)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _typeCapteurService = typeCapteurService;
        _variablesService = variablesService;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="prompt"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task<object> CreateRuleAsync(string prompt)
    {
        try
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(prompt))
            {
                throw new ArgumentException("Le prompt ne peut pas être vide", nameof(prompt));
            }

            if (prompt.Length > 1000)
            {
                throw new ArgumentException("Le prompt est trop long (maximum 1000 caractères)", nameof(prompt));
            }

            // Get all available TypeCapteur and Variables data
            var typeCapteurs = await _typeCapteurService.GetAll();
            var variables = await _variablesService.GetAll();

            // Validate that we have data to work with
            if (!typeCapteurs?.Any() == true)
            {
                throw new InvalidOperationException("Aucun type de capteur disponible dans le système");
            }

            if (!variables?.Any() == true)
            {
                throw new InvalidOperationException("Aucune variable disponible dans le système");
            }

            // Check if we have both sensors and actuators
            var sensors = typeCapteurs.Where(t => t.DeviceType?.ToLower() == "sensor").ToList();
            var actuators = typeCapteurs.Where(t => t.DeviceType?.ToLower() == "actuator").ToList();

            if (!sensors.Any())
            {
                throw new InvalidOperationException("Aucun capteur disponible pour créer des conditions");
            }

            if (!actuators.Any())
            {
                throw new InvalidOperationException("Aucun actuateur disponible pour créer des actions");
            }

            _logger.LogInformation("Generating rule with AI for prompt: {Prompt}. Available: {SensorCount} sensors, {ActuatorCount} actuators",
                prompt, sensors.Count, actuators.Count);

            // Generate rule using AI based on prompt and available data
            var generatedRule = await GenerateRuleWithAIAsync(prompt, typeCapteurs, variables);

            // Generate summary for the rule
            var summary = await GenerateRuleSummaryAsync(generatedRule, prompt);

            // Create and return the Rules object
            var rule = new
            {
                Summary = summary,
                RawData = JsonSerializer.Serialize(generatedRule, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                })
            };

            _logger.LogInformation("Successfully generated rule for prompt: {Prompt}", prompt);
            return rule;
        }
        catch (ArgumentException)
        {
            // Re-throw validation errors as-is
            throw;
        }
        catch (InvalidOperationException)
        {
            // Re-throw operation errors as-is
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error creating rule from prompt: {Prompt}", prompt);
            throw new InvalidOperationException("Erreur inattendue lors de la génération de la règle", ex);
        }
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="generatedRule"></param>
    /// <param name="originalPrompt"></param>
    /// <returns></returns>
    //  method to generate a better summary based on the actual rule
    private async Task<string> GenerateRuleSummaryAsync(object generatedRule, string originalPrompt)
    {
        try
        {
            var ruleJson = JsonSerializer.Serialize(generatedRule, new JsonSerializerOptions { WriteIndented = true });

            var summaryPrompt = $@"
Créez un résumé en français simple et compréhensible pour cette règle d'automatisation:

DEMANDE ORIGINALE: {originalPrompt}

RÈGLE GÉNÉRÉE:
{ruleJson}

Créez un résumé qui explique:
1. Ce qui déclenche la règle (conditions)
2. Ce qui se passe quand elle se déclenche (actions)
3. Dans quelles circonstances elle s'active

Le résumé doit être en français, en une ou deux phrases maximum, et compréhensible par un utilisateur non-technique.

Exemples:
- ""Cette règle allume automatiquement les lumières du salon quand un mouvement est détecté entre 18h et 23h.""
- ""Cette règle ferme les rideaux automatiquement quand la température dépasse 30 degrés.""

Résumé:";

            var summary = await SummarizeRuleAsync(summaryPrompt);

            // Clean up the summary response
            return summary?.Trim().Trim('"') ?? "Règle d'automatisation générée par IA";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to generate summary, using default");
            return $"Règle d'automatisation basée sur: {originalPrompt.Substring(0, Math.Min(100, originalPrompt.Length))}";
        }
    }
    /// <summary>
    /// ////////
    /// </summary>
    /// <param name="response"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    //  method to clean JSON response from AI
    private string CleanJsonResponse(string response)
    {
        if (string.IsNullOrWhiteSpace(response))
        {
            throw new InvalidOperationException("Réponse IA vide");
        }

        // Remove any text before the first {
        var startIndex = response.IndexOf('{');
        if (startIndex == -1)
        {
            throw new InvalidOperationException("Aucun JSON trouvé dans la réponse IA");
        }

        // Remove any text after the last }
        var endIndex = response.LastIndexOf('}');
        if (endIndex == -1 || endIndex < startIndex)
        {
            throw new InvalidOperationException("JSON malformé dans la réponse IA");
        }

        var cleanedResponse = response.Substring(startIndex, endIndex - startIndex + 1);

        // Additional cleaning
        cleanedResponse = cleanedResponse
            .Replace("```json", "")
            .Replace("```", "")
            .Trim();

        return cleanedResponse;
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="prompt"></param>
    /// <param name="typeCapteurs"></param>
    /// <param name="variables"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    private async Task<object> GenerateRuleWithAIAsync(string prompt, IEnumerable<TypeCapteur> typeCapteurs, IEnumerable<Variables> variables)
    {
        // Build context for AI
        var availableDevicesContext = BuildDeviceContext(typeCapteurs, variables);


        // Replace your aiPrompt variable with this enhanced version:

        // Replace your aiPrompt variable with this enhanced version that includes operators:

        // Replace your aiPrompt variable with this simplified working version:

        var aiPrompt = $@"
        Vous êtes un expert en règles d'automatisation IoT. Générez une règle JSON précise basée sur cette demande: '{prompt}'

        APPAREILS ET VARIABLES DISPONIBLES:
        {availableDevicesContext}

        TYPES DE CONDITIONS SUPPORTÉES:

        1. CONDITIONS CAPTEURS (sensors):
           - device: doit être exactement le ""DeviceKey"" (nom normalisé) du TypeCapteur
           - key: doit être exactement une ""Key"" de Variables associée
           - value: doit être une valeur du tableau ""Actions"" de cette Variable ou un nombre
           - operator: choisir parmi ""=="", ""!="", "">"", ""<"", "">="", ""<=""
           - type: ""payload""

        2. CONDITIONS TEMPORELLES (time):
           - type: ""time""
           - start_time: heure de début au format ""HH:mm""
           - end_time: heure de fin au format ""HH:mm""
           - Pour une heure précise: utilisez la même heure pour start_time et end_time
           - Format 24h: 12 AM = ""00:00"", 12 PM = ""12:00"", 6 PM = ""18:00""

        OPÉRATEURS POUR CONDITIONS CAPTEURS:
        - ""=="": égal à (par défaut si aucune indication)
        - ""!="": différent de
        - "">"": supérieur à
        - ""<"": inférieur à  
        - "">="": supérieur ou égal à
        - ""<="": inférieur ou égal à

        RÈGLES POUR TOPIC_PATTERN:
        - Ajoutez UNIQUEMENT les topics des capteurs utilisés dans les conditions payload
        - N'ajoutez JAMAIS les topics des actuators
        - Les conditions time n'ajoutent rien à topic_pattern

        EXEMPLES:

        EXEMPLE 1 - Temps seulement:
        ""close the curtains at 12am""
        {{
          ""rule_name"": ""Close curtains at midnight"",
          ""topic_pattern"": [],
          ""conditions"": {{
            ""operator"": ""AND"",
            ""groups"": [{{
              ""operator"": ""AND"",
              ""conditions"": [{{
                ""type"": ""time"",
                ""start_time"": ""00:00"",
                ""end_time"": ""00:00""
              }}]
            }}]
          }},
          ""actions"": [{{
            ""type"": ""position"",
            ""payload"": {{""position"": ""0""}},
            ""topic"": ""actuator/blinds_curtain""
          }}]
        }}

        EXEMPLE 2 - Capteur + temps:
        ""When the temperature is 27 and the time is 12Am, then close the curtains""
        {{
          ""rule_name"": ""Close curtains when temp 27 at midnight"",
          ""topic_pattern"": [""sensor/temp_humidity""],
          ""conditions"": {{
            ""operator"": ""AND"",
            ""groups"": [{{
              ""operator"": ""AND"",
              ""conditions"": [
                {{
                  ""type"": ""payload"",
                  ""device"": ""temperature_humidity_sensor"",
                  ""key"": ""temperature"",
                  ""operator"": ""=="",
                  ""value"": ""27""
                }},
                {{
                  ""type"": ""time"",
                  ""start_time"": ""00:00"",
                  ""end_time"": ""00:00""
                }}
              ]
            }}]
          }},
          ""actions"": [{{
            ""type"": ""position"",
            ""payload"": {{""position"": ""0""}},
            ""topic"": ""actuator/blinds_curtain""
          }}]
        }}

        EXEMPLE 3 - Avec opérateurs:
        ""close curtains when temperature above 30""
        {{
          ""rule_name"": ""Close curtains when hot"",
          ""topic_pattern"": [""sensor/temp_humidity""],
          ""conditions"": {{
            ""operator"": ""AND"",
            ""groups"": [{{
              ""operator"": ""AND"",
              ""conditions"": [{{
                ""type"": ""payload"",
                ""device"": ""temperature_humidity_sensor"",
                ""key"": ""temperature"",
                ""operator"": "">"",
                ""value"": ""30""
              }}]
            }}]
          }},
          ""actions"": [{{
            ""type"": ""position"",
            ""payload"": {{""position"": ""0""}},
            ""topic"": ""actuator/blinds_curtain""
          }}]
        }}

        PROCESSUS SIMPLE:
        1. Identifiez toutes les conditions dans la demande
        2. Pour chaque capteur mentionné, créez une condition payload
        3. Pour chaque heure mentionnée, créez une condition time
        4. Utilisez ""=="" par défaut, ou l'opérateur approprié si mentionné
        5. Ajoutez seulement les topics des capteurs à topic_pattern

        CONVERSION D'HEURES:
        - 12 AM = ""00:00""
        - 12 PM = ""12:00""
        - 6 PM = ""18:00""

        TEMPLATE JSON:
        {{
          ""rule_name"": ""[Nom descriptif]"",
          ""topic_pattern"": [""[topics des capteurs payload seulement]""],
          ""conditions"": {{
            ""operator"": ""AND"",
            ""groups"": [{{
              ""operator"": ""AND"",
              ""conditions"": [
                // Toutes les conditions identifiées
              ]
            }}]
          }},
          ""actions"": [{{
            ""type"": ""[Key de Variables actuator]"",
            ""payload"": {{""[Key]"": ""[valeur Actions]""}},
            ""topic"": ""[Topic actuator]""
          }}],
          ""schedule_config"": {{""enabled"": false}},
          ""enabled"": true,
          ""priority"": 1
        }}

        IMPORTANT: Si vous ne pouvez pas créer une règle valide, retournez: {{""error"": ""Impossible de créer une règle avec les appareils disponibles""}}

        Règle JSON:";

        var response = await CallGroqAsync(aiPrompt);

        try
        {
            // Clean and validate the AI response
            var cleanedResponse = CleanJsonResponse(response);

            // Check if AI returned an error
            if (cleanedResponse.Contains("\"error\""))
            {
                throw new InvalidOperationException("L'IA n'a pas pu générer une règle valide avec les appareils disponibles");
            }

            // Try to parse the AI response as JSON
            var ruleObject = JsonSerializer.Deserialize<object>(cleanedResponse);

            // Additional validation could be added here
            ValidateGeneratedRule(cleanedResponse, typeCapteurs, variables);

            return ruleObject;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "AI generated invalid JSON. Response: {Response}", response);
            throw new InvalidOperationException($"L'IA a généré un JSON invalide: {ex.Message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing AI generated rule");
            throw;
        }
    }

    private void ValidateGeneratedRule(string ruleJson, IEnumerable<TypeCapteur> typeCapteurs, IEnumerable<Variables> variables)
    {
        try
        {
            using var document = JsonDocument.Parse(ruleJson);
            var root = document.RootElement;

            // Validate conditions
            if (root.TryGetProperty("conditions", out var conditions) &&
                conditions.TryGetProperty("groups", out var groups))
            {
                foreach (var group in groups.EnumerateArray())
                {
                    if (group.TryGetProperty("conditions", out var conditionsList))
                    {
                        foreach (var condition in conditionsList.EnumerateArray())
                        {
                            ValidateCondition(condition, typeCapteurs, variables);
                        }
                    }
                }
            }

            // Validate actions
            if (root.TryGetProperty("actions", out var actions))
            {
                foreach (var action in actions.EnumerateArray())
                {
                    ValidateAction(action, typeCapteurs, variables);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Rule validation failed for: {RuleJson}", ruleJson);
            throw new InvalidOperationException("La règle générée ne respecte pas la structure des appareils disponibles");
        }
    }

    private void ValidateCondition(JsonElement condition, IEnumerable<TypeCapteur> typeCapteurs, IEnumerable<Variables> variables)
    {
        if (!condition.TryGetProperty("type", out var typeElement))
        {
            throw new InvalidOperationException("Condition invalide: type requis");
        }

        var conditionType = typeElement.GetString();

        switch (conditionType)
        {
            case "payload":
                ValidatePayloadCondition(condition, typeCapteurs, variables);
                break;

            case "time":
                ValidateTimeCondition(condition);
                break;

            default:
                throw new InvalidOperationException($"Type de condition non supporté: {conditionType}");
        }
    }
    private void ValidateTimeCondition(JsonElement condition)
    {
        if (!condition.TryGetProperty("start_time", out var startTimeElement) ||
            !condition.TryGetProperty("end_time", out var endTimeElement))
        {
            throw new InvalidOperationException("Condition time invalide: start_time et end_time requis");
        }

        var startTime = startTimeElement.GetString();
        var endTime = endTimeElement.GetString();

        // Validate time format (HH:mm)
        if (!IsValidTimeFormat(startTime))
        {
            throw new InvalidOperationException($"Format start_time invalide: '{startTime}'. Utilisez HH:mm (ex: 08:30)");
        }

        if (!IsValidTimeFormat(endTime))
        {
            throw new InvalidOperationException($"Format end_time invalide: '{endTime}'. Utilisez HH:mm (ex: 18:45)");
        }
    }

    private bool IsValidTimeFormat(string timeString)
    {
        if (string.IsNullOrEmpty(timeString))
            return false;

        // Check if it matches HH:mm format
        if (timeString.Length != 5 || timeString[2] != ':')
            return false;

        // Try to parse as TimeSpan
        return TimeSpan.TryParseExact(timeString, @"hh\:mm", null, out _);
    }
    private void ValidatePayloadCondition(JsonElement condition, IEnumerable<TypeCapteur> typeCapteurs, IEnumerable<Variables> variables)
    {
        if (!condition.TryGetProperty("device", out var deviceElement) ||
            !condition.TryGetProperty("key", out var keyElement))
        {
            throw new InvalidOperationException("Condition payload invalide: device et key requis");
        }

        var deviceKey = deviceElement.GetString();
        var keyName = keyElement.GetString();

        // Find the TypeCapteur by normalized device key
        var typeCapteur = typeCapteurs.FirstOrDefault(t =>
            NormalizeDeviceKey(t.Nom) == deviceKey);

        if (typeCapteur == null)
        {
            throw new InvalidOperationException($"Appareil avec DeviceKey '{deviceKey}' non trouvé");
        }

        // Check if it's a sensor
        if (typeCapteur.DeviceType?.ToLower() != "sensor")
        {
            throw new InvalidOperationException($"L'appareil '{deviceKey}' n'est pas un capteur");
        }

        // Find the variable
        var variable = variables.FirstOrDefault(v => v.IdTypeCapteur == typeCapteur.Id && v.Key == keyName);
        if (variable == null)
        {
            throw new InvalidOperationException($"Variable '{keyName}' non trouvée pour l'appareil '{deviceKey}'");
        }
    }

    private void ValidateAction(JsonElement action, IEnumerable<TypeCapteur> typeCapteurs, IEnumerable<Variables> variables)
    {
        if (!action.TryGetProperty("type", out var typeElement) ||
            !action.TryGetProperty("topic", out var topicElement))
        {
            throw new InvalidOperationException("Action invalide: type et topic requis");
        }

        var actionType = typeElement.GetString();
        var actionTopic = topicElement.GetString();

        // Find the TypeCapteur by topic
        var typeCapteur = typeCapteurs.FirstOrDefault(t => t.Topic == actionTopic);
        if (typeCapteur == null)
        {
            throw new InvalidOperationException($"Appareil avec topic '{actionTopic}' non trouvé");
        }

        // Check if it's an actuator
        if (typeCapteur.DeviceType?.ToLower() != "actuator")
        {
            throw new InvalidOperationException($"L'appareil avec topic '{actionTopic}' n'est pas un actuateur");
        }

        // Find the variable
        var variable = variables.FirstOrDefault(v => v.IdTypeCapteur == typeCapteur.Id && v.Key == actionType);
        if (variable == null)
        {
            throw new InvalidOperationException($"Type d'action '{actionType}' non trouvé pour l'appareil '{typeCapteur.Nom}'");
        }
    }

    private string BuildDeviceContext(IEnumerable<TypeCapteur> typeCapteurs, IEnumerable<Variables> variables)
    {
        var context = new StringBuilder();

        // Group by device type for better clarity
        var sensors = typeCapteurs.Where(t => t.DeviceType?.ToLower() == "sensor").ToList();
        var actuators = typeCapteurs.Where(t => t.DeviceType?.ToLower() == "actuator").ToList();

        context.AppendLine("=== CAPTEURS (SENSORS) - Pour les CONDITIONS ===");
        foreach (var sensor in sensors)
        {
            // Generate the normalized device key like the frontend does
            var deviceKey = NormalizeDeviceKey(sensor.Nom);

            context.AppendLine($"CAPTEUR: {sensor.DisplayName}");
            context.AppendLine($"  DeviceKey: \"{deviceKey}\" (UTILISEZ CECI POUR device)");
            context.AppendLine($"  DisplayName: \"{sensor.DisplayName}\" (pour référence)");
            context.AppendLine($"  Topic: {sensor.Topic} (pour topic_pattern)");

            var sensorVariables = variables.Where(v => v.IdTypeCapteur == sensor.Id).ToList();
            if (sensorVariables.Any())
            {
                context.AppendLine("  Variables disponibles:");
                foreach (var variable in sensorVariables)
                {
                    var actionsStr = variable.Actions?.Any() == true ?
                        $"[\"{string.Join("\", \"", variable.Actions)}\"]" : "[]";
                    context.AppendLine($"    - Key: \"{variable.Key}\" (Type: {variable.Type}) Actions: {actionsStr}");
                }
            }
            context.AppendLine();
        }

        context.AppendLine("=== ACTUATEURS (ACTUATORS) - Pour les ACTIONS ===");
        foreach (var actuator in actuators)
        {
            // Generate the normalized device key like the frontend does
            var deviceKey = NormalizeDeviceKey(actuator.Nom);

            context.AppendLine($"ACTUATEUR: {actuator.DisplayName}");
            context.AppendLine($"  DeviceKey: \"{deviceKey}\" (pour référence)");
            context.AppendLine($"  DisplayName: \"{actuator.DisplayName}\" (pour référence)");
            context.AppendLine($"  Topic: {actuator.Topic} (UTILISEZ CECI POUR topic des actions)");

            var actuatorVariables = variables.Where(v => v.IdTypeCapteur == actuator.Id).ToList();
            if (actuatorVariables.Any())
            {
                context.AppendLine("  Actions disponibles:");
                foreach (var variable in actuatorVariables)
                {
                    var actionsStr = variable.Actions?.Any() == true ?
                        $"[\"{string.Join("\", \"", variable.Actions)}\"]" : "[]";
                    context.AppendLine($"    - Key: \"{variable.Key}\" (Type: {variable.Type}) Valeurs: {actionsStr}");
                }
            }
            context.AppendLine();
        }

        return context.ToString();
    }

    // Add this helper method to match the frontend normalization
    private string NormalizeDeviceKey(string name)
    {
        return name.ToLowerInvariant()
            .Replace(" ", "_")
            .Replace("/", "_")
            .Replace("-", "_")
            .Replace("(", "")
            .Replace(")", "")
            .Replace(",", "")
            .Replace(".", "")
            .Trim('_');
    }





    /// <summary>
    /// 
    /// </summary>
    /// <param name="jsonPrompt"></param>
    /// <returns></returns>

    public async Task<string> SummarizeLogAsync(string jsonPrompt)
    {
        var prompt = $@"Analysez ce JSON de log et donnez un résumé français en 1-2 phrases:

{jsonPrompt}

Résumé français:";

        return await CallGroqAsync(prompt);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="jsonPrompt"></param>
    /// <returns></returns>

    public async Task<string> SummarizeRuleAsync(string jsonPrompt)
    {
        var prompt = $@"Analysez cette règle d'automatisation JSON et donnez un résumé français en 1-2 phrases:

{jsonPrompt}

Résumé français:";

        return await CallGroqAsync(prompt);
    }

    /// <summary>
    /// ////////
    /// </summary>
    /// <param name="prompt"></param>
    /// <returns></returns>
    private async Task<string> CallGroqAsync(string prompt)
    {
        try
        {
            var baseUrl = _configuration["Groq:BaseUrl"];
            var apiKey = _configuration["Groq:ApiKey"];
            var model = _configuration["Groq:Model"];

            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new {
                        role = "system",
                        content = "Tu es un assistant expert qui analyse des données JSON et fournit des résumés clairs en français."
                    },
                    new {
                        role = "user",
                        content = prompt
                    }
                },
                max_tokens = 500, // Increased for rule generation
                temperature = 0.3
            };

            var jsonContent = new StringContent(
                JsonSerializer.Serialize(requestBody),
                Encoding.UTF8,
                "application/json");

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);

            var response = await _httpClient.PostAsync(baseUrl, jsonContent);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Groq API Error {StatusCode}: {ErrorContent}", response.StatusCode, errorContent);
                return "Erreur du service Groq. Vérifiez votre clé API.";
            }

            var responseBody = await response.Content.ReadAsStringAsync();
            using var doc = JsonDocument.Parse(responseBody);

            var choices = doc.RootElement.GetProperty("choices");
            if (choices.GetArrayLength() > 0)
            {
                var content = choices[0]
                    .GetProperty("message")
                    .GetProperty("content")
                    .GetString();

                return content?.Trim() ?? "Pas de réponse générée.";
            }

            return "Format de réponse inattendu.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Groq API error");
            return "Erreur lors de l'appel à Groq.";
        }
    }
}