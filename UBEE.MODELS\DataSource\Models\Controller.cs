﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Controller : AuditEntity
{
    public string HostName { get; set; } //Nom de l'hote
    public string Model { get; set; } // Modele
    public string SerialNumber { get; set; } //Numero de serie
    public string MacAddress { get; set; } // Mac Adresse
    public string IpAddress { get; set; } // Ip adresse
    public DateTime LastConnection { get; set; } // Date time de la dernière connexion
    public bool State { get; set; } // L'etat du contrôleur (actif ou inactif)
    public string BaseTopic { get; set; }
    public DateOnly InstallationDate { get; set; } //Date d'installation
    public List<Transaction> Transactions { get; set; }
    public List<ControllerServerController> ControllerServerControllers { get; set; }
}
