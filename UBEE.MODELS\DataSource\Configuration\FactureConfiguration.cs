﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class FactureConfiguration : IEntityTypeConfiguration<Facture>
{
    public void Configure(EntityTypeBuilder<Facture> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasOne(s => s.Licence)
        .WithMany(s => s.Factures)
        .HasForeignKey(s => s.IdLicence);
    }
}
