﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/controller-serveur")]
[ApiController]
public class ControllerServeurController : BaseController<ControllerServeur>
{
    private readonly IControllerServeurService _service;
    public ControllerServeurController(IControllerServeurService baseService) : base(baseService)
    {
        this._service = baseService as IControllerServeurService;
        this.UnwantedProperties += "";
        IncludesProperties = "ControllerServerControllers.Controller,Licence";
    }
}

