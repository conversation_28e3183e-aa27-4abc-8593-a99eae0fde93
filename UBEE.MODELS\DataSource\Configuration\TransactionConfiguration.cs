﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class TransactionConfiguration : IEntityTypeConfiguration<Transaction>
{
    public void Configure(EntityTypeBuilder<Transaction> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasOne(s => s.Controller)
        .WithMany(s => s.Transactions)
        .HasForeignKey(s => s.IdController);

        builder.HasOne(s => s.Local)
        .WithMany(s => s.Transactions)
        .HasForeignKey(s => s.IdLocal);

        builder.<PERSON>One(s => s.Capteur)
        .WithMany(s => s.Transactions)
        .HasForeignKey(s => s.IdCapteur);

        builder.HasMany(s => s.Logs)
        .WithOne(s => s.Transaction)
        .HasForeignKey(s => s.TransactionId);
    }
}
