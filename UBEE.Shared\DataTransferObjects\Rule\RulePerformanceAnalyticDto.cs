﻿namespace UBEE.MODELS.DataSource.DTOs
{
    public class RulePerformanceAnalyticDto
    {
        public Guid RuleId { get; set; }
        public DateTime ApplicationDate { get; set; }
        public int ApplicationHour { get; set; }
        public string ApplicationDayOfWeek { get; set; } // DATENAME returns a string

        public int TotalApplications { get; set; }
        public int SuccessfulApplications { get; set; } // Currently always 0 based on view
        public int FailedApplications { get; set; }     // Currently always 0 based on view
        public double? AvgExecutionTimeMs { get; set; } // Nullable if no transactions
        public Guid LocalId { get; set; }
        public string LocalName { get; set; }
    }
}